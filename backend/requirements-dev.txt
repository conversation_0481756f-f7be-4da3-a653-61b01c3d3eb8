# ============================================================================
# RAG-Chat 开发环境依赖配置
# ============================================================================

# 引入生产环境依赖
-r requirements.txt

# ============================================================================
# 开发工具
# ============================================================================

# 代码格式化和检查
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.0.0

# 开发辅助工具
ipython>=8.0.0
jupyter>=1.0.0
pre-commit>=3.0.0

# ============================================================================
# 额外测试工具
# ============================================================================

# 测试增强工具
pytest-mock==3.10.0
pytest-xdist==3.3.1
mock==5.0.2
fakeredis==2.20.0
redis-mock==0.1.3

# 性能测试
locust>=2.0.0

# API测试
requests>=2.28.0
