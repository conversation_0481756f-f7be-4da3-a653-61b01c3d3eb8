{"meta": {"format": 3, "version": "7.8.2", "timestamp": "2025-06-27T22:36:14.448306", "branch_coverage": false, "show_contexts": false}, "files": {"app/admin/__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/admin/auth.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 67, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 16, 19, 20, 23, 24, 25, 27, 28, 30, 31, 32, 33, 35, 36, 39, 48, 50, 52, 54, 56, 58, 59, 60, 61, 63, 65, 66, 67, 68, 69, 70, 72, 74, 75, 76, 78, 79, 80, 81, 83, 85, 90, 91, 92, 93, 94, 95, 96, 97, 99, 100, 101, 103, 104, 106, 108, 110], "excluded_lines": [], "functions": {"verify_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [50], "excluded_lines": []}, "get_password_hash": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [54], "excluded_lines": []}, "get_admin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [58, 59, 60, 61], "excluded_lines": []}, "authenticate_admin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [65, 66, 67, 68, 69, 70], "excluded_lines": []}, "create_access_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [74, 75, 76, 78, 79, 80, 81], "excluded_lines": []}, "get_current_admin": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [85, 90, 91, 92, 93, 94, 95, 96, 97, 99, 100, 101, 103, 104, 106], "excluded_lines": []}, "get_admin_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [110], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 32, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 32, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 16, 19, 20, 23, 24, 25, 27, 28, 30, 31, 32, 33, 35, 36, 39, 48, 52, 56, 63, 72, 83, 108], "excluded_lines": []}}, "classes": {"Token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TokenData": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AdminBase": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AdminInDB": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 67, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 67, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 16, 19, 20, 23, 24, 25, 27, 28, 30, 31, 32, 33, 35, 36, 39, 48, 50, 52, 54, 56, 58, 59, 60, 61, 63, 65, 66, 67, 68, 69, 70, 72, 74, 75, 76, 78, 79, 80, 81, 83, 85, 90, 91, 92, 93, 94, 95, 96, 97, 99, 100, 101, 103, 104, 106, 108, 110], "excluded_lines": []}}}, "app/admin/endpoints/__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/admin/endpoints/auth.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 9, 16, 18, 20, 21, 23, 24, 25, 26, 33, 34, 38, 40, 42, 43, 45], "excluded_lines": [], "functions": {"login_for_access_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [23, 24, 25, 26, 33, 34, 38, 40], "excluded_lines": []}, "read_users_me": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [45], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 9, 16, 18, 20, 21, 42, 43], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 9, 16, 18, 20, 21, 23, 24, 25, 26, 33, 34, 38, 40, 42, 43, 45], "excluded_lines": []}}}, "app/admin/endpoints/mongodb_admin.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 84, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 84, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 9, 10, 17, 20, 22, 24, 25, 27, 28, 29, 30, 31, 32, 34, 35, 45, 47, 48, 49, 52, 53, 54, 55, 56, 57, 58, 61, 64, 65, 66, 69, 72, 73, 76, 79, 80, 82, 83, 85, 94, 95, 96, 97, 98, 100, 101, 107, 109, 110, 111, 114, 117, 119, 124, 125, 126, 128, 129, 135, 137, 138, 139, 142, 148, 150, 156, 157, 158, 160, 161, 167, 169, 170, 171, 174, 177, 179, 184, 185, 186], "excluded_lines": [], "functions": {"list_collections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [27, 28, 29, 30, 31, 32], "excluded_lines": []}, "get_collection_data": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 29, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 29, "excluded_lines": 0}, "missing_lines": [45, 47, 48, 49, 52, 53, 54, 55, 56, 57, 58, 61, 64, 65, 66, 69, 72, 73, 76, 79, 80, 82, 83, 85, 94, 95, 96, 97, 98], "excluded_lines": []}, "insert_document": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [107, 109, 110, 111, 114, 117, 119, 124, 125, 126], "excluded_lines": []}, "update_documents": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [135, 137, 138, 139, 142, 148, 150, 156, 157, 158], "excluded_lines": []}, "delete_documents": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [167, 169, 170, 171, 174, 177, 179, 184, 185, 186], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 9, 10, 17, 20, 22, 24, 25, 34, 35, 100, 101, 128, 129, 160, 161], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 84, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 84, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 9, 10, 17, 20, 22, 24, 25, 27, 28, 29, 30, 31, 32, 34, 35, 45, 47, 48, 49, 52, 53, 54, 55, 56, 57, 58, 61, 64, 65, 66, 69, 72, 73, 76, 79, 80, 82, 83, 85, 94, 95, 96, 97, 98, 100, 101, 107, 109, 110, 111, 114, 117, 119, 124, 125, 126, 128, 129, 135, 137, 138, 139, 142, 148, 150, 156, 157, 158, 160, 161, 167, 169, 170, 171, 174, 177, 179, 184, 185, 186], "excluded_lines": []}}}, "app/admin/endpoints/system_admin.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 145, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 145, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 18, 20, 22, 24, 26, 27, 30, 31, 33, 35, 36, 38, 39, 42, 43, 45, 46, 47, 48, 49, 50, 51, 52, 54, 55, 56, 59, 61, 62, 64, 65, 66, 68, 69, 70, 72, 75, 76, 77, 78, 79, 80, 82, 85, 86, 87, 89, 90, 92, 98, 99, 100, 101, 102, 104, 105, 106, 108, 109, 110, 111, 113, 114, 116, 118, 119, 122, 125, 128, 130, 151, 152, 154, 155, 156, 157, 159, 160, 162, 164, 176, 177, 189, 190, 198, 204, 205, 206, 208, 209, 215, 217, 218, 219, 221, 222, 223, 226, 227, 229, 230, 231, 234, 235, 238, 239, 246, 253, 255, 261, 262, 263, 265, 266, 272, 274, 275, 278, 279, 282, 283, 286, 287, 288, 289, 291, 292, 293, 295, 301, 302, 303, 304, 305], "excluded_lines": [], "functions": {"get_mac_gpu_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 58, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 58, "excluded_lines": 0}, "missing_lines": [24, 26, 27, 30, 31, 33, 35, 36, 38, 39, 42, 43, 45, 46, 47, 48, 49, 50, 51, 52, 54, 55, 56, 59, 61, 62, 64, 65, 66, 68, 69, 70, 72, 75, 76, 77, 78, 79, 80, 82, 85, 86, 87, 89, 90, 92, 98, 99, 100, 101, 102, 104, 105, 106, 108, 109, 110, 111], "excluded_lines": []}, "get_system_metrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [116, 118, 119, 122, 125, 128, 130, 151, 152, 154, 155, 156, 157], "excluded_lines": []}, "get_system_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [162, 164, 176, 177, 189, 190, 198, 204, 205, 206], "excluded_lines": []}, "get_processes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [215, 217, 218, 219, 221, 222, 223, 226, 227, 229, 230, 231, 234, 235, 238, 239, 246, 253, 255, 261, 262, 263], "excluded_lines": []}, "get_system_logs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [272, 274, 275, 278, 279, 282, 283, 286, 287, 288, 289, 291, 292, 293, 295, 301, 302, 303, 304, 305], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 18, 20, 22, 113, 114, 159, 160, 208, 209, 265, 266], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 145, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 145, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 15, 18, 20, 22, 24, 26, 27, 30, 31, 33, 35, 36, 38, 39, 42, 43, 45, 46, 47, 48, 49, 50, 51, 52, 54, 55, 56, 59, 61, 62, 64, 65, 66, 68, 69, 70, 72, 75, 76, 77, 78, 79, 80, 82, 85, 86, 87, 89, 90, 92, 98, 99, 100, 101, 102, 104, 105, 106, 108, 109, 110, 111, 113, 114, 116, 118, 119, 122, 125, 128, 130, 151, 152, 154, 155, 156, 157, 159, 160, 162, 164, 176, 177, 189, 190, 198, 204, 205, 206, 208, 209, 215, 217, 218, 219, 221, 222, 223, 226, 227, 229, 230, 231, 234, 235, 238, 239, 246, 253, 255, 261, 262, 263, 265, 266, 272, 274, 275, 278, 279, 282, 283, 286, 287, 288, 289, 291, 292, 293, 295, 301, 302, 303, 304, 305], "excluded_lines": []}}}, "app/admin/endpoints/vector_admin.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 129, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 129, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 8, 10, 11, 15, 18, 20, 23, 24, 25, 26, 27, 29, 30, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 44, 50, 51, 57, 58, 60, 61, 62, 65, 66, 67, 68, 69, 72, 73, 74, 75, 77, 78, 79, 80, 81, 82, 84, 85, 90, 91, 92, 95, 96, 97, 100, 103, 105, 106, 107, 109, 115, 116, 117, 118, 120, 121, 122, 123, 124, 126, 127, 133, 134, 135, 138, 139, 140, 143, 144, 145, 148, 151, 152, 160, 166, 172, 173, 174, 175, 176, 178, 179, 184, 185, 186, 189, 190, 191, 194, 195, 198, 200, 204, 205, 206, 208, 209, 214, 215, 216, 219, 220, 221, 224, 227, 230, 233, 234, 237, 238, 241, 243, 248, 249, 250], "excluded_lines": [], "functions": {"check_vector_store_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 44, 50, 51], "excluded_lines": []}, "list_vector_collections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [60, 61, 62, 65, 66, 67, 68, 69, 72, 73, 74, 75, 77, 78, 79, 80, 81, 82], "excluded_lines": []}, "get_collection_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [90, 91, 92, 95, 96, 97, 100, 103, 105, 106, 107, 109, 115, 116, 117, 118, 120, 121, 122, 123, 124], "excluded_lines": []}, "get_collection_sample": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [133, 134, 135, 138, 139, 140, 143, 144, 145, 148, 151, 152, 160, 166, 172, 173, 174, 175, 176], "excluded_lines": []}, "flush_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [184, 185, 186, 189, 190, 191, 194, 195, 198, 200, 204, 205, 206], "excluded_lines": []}, "purge_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [214, 215, 216, 219, 220, 221, 224, 227, 230, 233, 234, 237, 238, 241, 243, 248, 249, 250], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 8, 10, 11, 15, 18, 20, 23, 24, 25, 26, 27, 29, 30, 57, 58, 84, 85, 126, 127, 178, 179, 208, 209], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 129, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 129, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 7, 8, 10, 11, 15, 18, 20, 23, 24, 25, 26, 27, 29, 30, 32, 33, 34, 36, 37, 38, 39, 40, 41, 42, 44, 50, 51, 57, 58, 60, 61, 62, 65, 66, 67, 68, 69, 72, 73, 74, 75, 77, 78, 79, 80, 81, 82, 84, 85, 90, 91, 92, 95, 96, 97, 100, 103, 105, 106, 107, 109, 115, 116, 117, 118, 120, 121, 122, 123, 124, 126, 127, 133, 134, 135, 138, 139, 140, 143, 144, 145, 148, 151, 152, 160, 166, 172, 173, 174, 175, 176, 178, 179, 184, 185, 186, 189, 190, 191, 194, 195, 198, 200, 204, 205, 206, 208, 209, 214, 215, 216, 219, 220, 221, 224, 227, 230, 233, 234, 237, 238, 241, 243, 248, 249, 250], "excluded_lines": []}}}, "app/admin/router.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [4, 5, 8, 15, 18, 21, 24], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [4, 5, 8, 15, 18, 21, 24], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [4, 5, 8, 15, 18, 21, 24], "excluded_lines": []}}}, "app/admin/schemas/__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/admin/schemas/admin.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 71, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 71, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 9, 10, 11, 12, 14, 15, 16, 18, 19, 20, 21, 22, 24, 25, 27, 28, 29, 31, 32, 34, 35, 38, 39, 40, 42, 43, 45, 46, 47, 48, 49, 52, 53, 54, 56, 57, 58, 59, 60, 62, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 84, 85, 86, 87, 88, 89, 90, 92, 93, 94, 95], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 71, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 71, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 9, 10, 11, 12, 14, 15, 16, 18, 19, 20, 21, 22, 24, 25, 27, 28, 29, 31, 32, 34, 35, 38, 39, 40, 42, 43, 45, 46, 47, 48, 49, 52, 53, 54, 56, 57, 58, 59, 60, 62, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 84, 85, 86, 87, 88, 89, 90, 92, 93, 94, 95], "excluded_lines": []}}, "classes": {"CollectionInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentItem": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MongoDBCollectionResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MongoDBCollectionsResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentUpdateRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentDeleteRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentInsertRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "VectorCollectionInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "VectorCollectionsResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "VectorCollectionStatsResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CPUMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MemoryMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DiskMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GPUMetrics": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SystemMetricsResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LogEntry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LogsResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 71, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 71, "excluded_lines": 0}, "missing_lines": [4, 5, 6, 9, 10, 11, 12, 14, 15, 16, 18, 19, 20, 21, 22, 24, 25, 27, 28, 29, 31, 32, 34, 35, 38, 39, 40, 42, 43, 45, 46, 47, 48, 49, 52, 53, 54, 56, 57, 58, 59, 60, 62, 63, 64, 65, 66, 68, 69, 70, 71, 72, 73, 74, 76, 77, 78, 79, 80, 81, 84, 85, 86, 87, 88, 89, 90, 92, 93, 94, 95], "excluded_lines": []}}}, "app/api/deps.py": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 13, 16, 22, 23, 24, 29, 31, 103, 111], "summary": {"covered_lines": 18, "num_statements": 60, "percent_covered": 30.0, "percent_covered_display": "30", "missing_lines": 42, "excluded_lines": 0}, "missing_lines": [17, 18, 19, 26, 49, 50, 51, 52, 64, 70, 71, 73, 74, 75, 76, 77, 78, 80, 81, 82, 83, 85, 86, 87, 88, 89, 91, 92, 93, 95, 96, 97, 98, 99, 100, 101, 107, 108, 109, 115, 116, 117], "excluded_lines": [], "functions": {"OptionalOAuth2.__call__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [17, 18, 19], "excluded_lines": []}, "get_current_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 32, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 32, "excluded_lines": 0}, "missing_lines": [49, 50, 51, 52, 64, 70, 71, 73, 74, 75, 76, 77, 78, 80, 81, 82, 83, 85, 86, 87, 88, 89, 91, 92, 93, 95, 96, 97, 98, 99, 100, 101], "excluded_lines": []}, "get_current_active_user": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [107, 108, 109], "excluded_lines": []}, "get_current_superuser": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [115, 116, 117], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 13, 16, 22, 23, 24, 29, 31, 103, 111], "summary": {"covered_lines": 18, "num_statements": 19, "percent_covered": 94.73684210526316, "percent_covered_display": "95", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [26], "excluded_lines": []}}, "classes": {"OptionalOAuth2": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [17, 18, 19], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 12, 13, 16, 22, 23, 24, 29, 31, 103, 111], "summary": {"covered_lines": 18, "num_statements": 57, "percent_covered": 31.57894736842105, "percent_covered_display": "32", "missing_lines": 39, "excluded_lines": 0}, "missing_lines": [26, 49, 50, 51, 52, 64, 70, 71, 73, 74, 75, 76, 77, 78, 80, 81, 82, 83, 85, 86, 87, 88, 89, 91, 92, 93, 95, 96, 97, 98, 99, 100, 101, 107, 108, 109, 115, 116, 117], "excluded_lines": []}}}, "app/api/v1/endpoints/discover.py": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 15, 17, 19, 20, 34, 36, 38, 39, 40, 43, 45, 46, 47, 49, 50, 51, 52, 54, 55, 56, 57, 58, 59, 60, 62, 63], "summary": {"covered_lines": 32, "num_statements": 44, "percent_covered": 72.72727272727273, "percent_covered_display": "73", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [94, 96, 97, 98, 99, 102, 115, 116, 117, 118, 119, 120], "excluded_lines": [], "functions": {"discover_models": {"executed_lines": [34, 36, 38, 39, 40, 43, 45, 46, 47, 49, 50, 51, 52, 54, 55, 56, 57, 58, 59, 60], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "register_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [94, 96, 97, 98, 99, 102, 115, 116, 117, 118, 119, 120], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 15, 17, 19, 20, 62, 63], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 7, 8, 9, 10, 11, 12, 15, 17, 19, 20, 34, 36, 38, 39, 40, 43, 45, 46, 47, 49, 50, 51, 52, 54, 55, 56, 57, 58, 59, 60, 62, 63], "summary": {"covered_lines": 32, "num_statements": 44, "percent_covered": 72.72727272727273, "percent_covered_display": "73", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [94, 96, 97, 98, 99, 102, 115, 116, 117, 118, 119, 120], "excluded_lines": []}}}, "app/config.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [3, 4, 5, 7, 11, 14, 15, 19, 20, 22], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [3, 4, 5, 7, 11, 14, 15, 19, 20, 22], "excluded_lines": []}}, "classes": {"Settings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Settings.Config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [3, 4, 5, 7, 11, 14, 15, 19, 20, 22], "excluded_lines": []}}}, "app/core/config.py": {"executed_lines": [1, 16, 17, 18, 19, 20, 21, 27, 29, 30, 37, 38, 41, 44, 47, 48, 51, 52, 55, 56, 57, 60, 61, 64, 65, 68, 69, 70, 71, 74, 81, 84, 86, 87, 89, 92, 95, 96, 97, 99, 100, 115, 117, 118, 121, 128], "summary": {"covered_lines": 44, "num_statements": 46, "percent_covered": 95.65217391304348, "percent_covered_display": "96", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [116, 119], "excluded_lines": [], "functions": {"Settings.assemble_cors_origins": {"executed_lines": [115, 117, 118], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [116, 119], "excluded_lines": []}, "": {"executed_lines": [1, 16, 17, 18, 19, 20, 21, 27, 29, 30, 37, 38, 41, 44, 47, 48, 51, 52, 55, 56, 57, 60, 61, 64, 65, 68, 69, 70, 71, 74, 81, 84, 86, 87, 89, 92, 95, 96, 97, 99, 100, 121, 128], "summary": {"covered_lines": 41, "num_statements": 41, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"Settings": {"executed_lines": [115, 117, 118], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [116, 119], "excluded_lines": []}, "": {"executed_lines": [1, 16, 17, 18, 19, 20, 21, 27, 29, 30, 37, 38, 41, 44, 47, 48, 51, 52, 55, 56, 57, 60, 61, 64, 65, 68, 69, 70, 71, 74, 81, 84, 86, 87, 89, 92, 95, 96, 97, 99, 100, 121, 128], "summary": {"covered_lines": 41, "num_statements": 41, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/core/paths.py": {"executed_lines": [1, 2, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 21, 22, 23, 26, 27, 28, 29, 30, 31, 32, 35, 42, 45, 47, 48], "summary": {"covered_lines": 29, "num_statements": 29, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"get_env_path": {"executed_lines": [47, 48], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 2, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 21, 22, 23, 26, 27, 28, 29, 30, 31, 32, 35, 42, 45], "summary": {"covered_lines": 27, "num_statements": 27, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 2, 5, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 20, 21, 22, 23, 26, 27, 28, 29, 30, 31, 32, 35, 42, 45, 47, 48], "summary": {"covered_lines": 29, "num_statements": 29, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/core/security.py": {"executed_lines": [1, 12, 13, 14, 15, 16, 19, 21, 36, 50], "summary": {"covered_lines": 9, "num_statements": 18, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [34, 48, 63, 64, 65, 67, 68, 69, 70], "excluded_lines": [], "functions": {"verify_password": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [34], "excluded_lines": []}, "get_password_hash": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [48], "excluded_lines": []}, "create_access_token": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [63, 64, 65, 67, 68, 69, 70], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 15, 16, 19, 21, 36, 50], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 12, 13, 14, 15, 16, 19, 21, 36, 50], "summary": {"covered_lines": 9, "num_statements": 18, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [34, 48, 63, 64, 65, 67, 68, 69, 70], "excluded_lines": []}}}, "app/db/document_store.py": {"executed_lines": [1, 2, 3, 5, 7, 8, 10, 15, 25, 42, 55, 69, 81], "summary": {"covered_lines": 12, "num_statements": 64, "percent_covered": 18.75, "percent_covered_display": "19", "missing_lines": 52, "excluded_lines": 0}, "missing_lines": [11, 12, 13, 17, 18, 19, 20, 21, 22, 23, 27, 28, 29, 32, 33, 35, 36, 38, 39, 40, 44, 45, 46, 48, 49, 50, 51, 52, 53, 57, 58, 59, 61, 62, 63, 64, 65, 66, 67, 71, 73, 75, 76, 77, 78, 79, 83, 84, 88, 89, 90, 91], "excluded_lines": [], "functions": {"DocumentStore.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [11, 12, 13], "excluded_lines": []}, "DocumentStore.store_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [17, 18, 19, 20, 21, 22, 23], "excluded_lines": []}, "DocumentStore.store_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [27, 28, 29, 32, 33, 35, 36, 38, 39, 40], "excluded_lines": []}, "DocumentStore.get_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [44, 45, 46, 48, 49, 50, 51, 52, 53], "excluded_lines": []}, "DocumentStore.get_chunks_by_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [57, 58, 59, 61, 62, 63, 64, 65, 66, 67], "excluded_lines": []}, "DocumentStore.delete_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [71, 73, 75, 76, 77, 78, 79], "excluded_lines": []}, "DocumentStore.update_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [83, 84, 88, 89, 90, 91], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 5, 7, 8, 10, 15, 25, 42, 55, 69, 81], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DocumentStore": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 52, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 52, "excluded_lines": 0}, "missing_lines": [11, 12, 13, 17, 18, 19, 20, 21, 22, 23, 27, 28, 29, 32, 33, 35, 36, 38, 39, 40, 44, 45, 46, 48, 49, 50, 51, 52, 53, 57, 58, 59, 61, 62, 63, 64, 65, 66, 67, 71, 73, 75, 76, 77, 78, 79, 83, 84, 88, 89, 90, 91], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 5, 7, 8, 10, 15, 25, 42, 55, 69, 81], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/db/mongodb.py": {"executed_lines": [1, 12, 13, 14, 16, 18, 19, 25, 26, 27, 29, 49, 58], "summary": {"covered_lines": 11, "num_statements": 26, "percent_covered": 42.30769230769231, "percent_covered_display": "42", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [31, 33, 34, 37, 38, 41, 43, 45, 46, 47, 51, 52, 53, 54, 55], "excluded_lines": [], "functions": {"MongoDB.__init__": {"executed_lines": [26, 27], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MongoDB.connect": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [31, 33, 34, 37, 38, 41, 43, 45, 46, 47], "excluded_lines": []}, "MongoDB.close": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [51, 52, 53, 54, 55], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 16, 18, 19, 25, 29, 49, 58], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"MongoDB": {"executed_lines": [26, 27], "summary": {"covered_lines": 2, "num_statements": 17, "percent_covered": 11.764705882352942, "percent_covered_display": "12", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [31, 33, 34, 37, 38, 41, 43, 45, 46, 47, 51, 52, 53, 54, 55], "excluded_lines": []}, "": {"executed_lines": [1, 12, 13, 14, 16, 18, 19, 25, 29, 49, 58], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/main.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 69, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 69, "excluded_lines": 0}, "missing_lines": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 27, 36, 38, 39, 46, 48, 49, 50, 53, 54, 55, 56, 59, 60, 61, 62, 64, 67, 68, 69, 71, 79, 88, 89, 95, 96, 99, 100, 103, 104, 117, 119, 120, 122, 127, 129, 132, 133, 134, 143, 150, 152, 153, 159, 160, 173, 174, 179, 180, 189, 192, 193, 199, 202, 203, 209], "excluded_lines": [], "functions": {"lifespan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [46, 48, 49, 50, 53, 54, 55, 56, 59, 60, 61, 62, 64, 67, 68, 69], "excluded_lines": []}, "validation_exception_handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [117, 119, 120, 122, 127, 129, 132, 133, 134, 143, 150, 152, 153], "excluded_lines": []}, "global_exception_handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [173, 174], "excluded_lines": []}, "root": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [189], "excluded_lines": []}, "startup_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [199], "excluded_lines": []}, "shutdown_event": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [209], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 27, 36, 38, 39, 71, 79, 88, 89, 95, 96, 99, 100, 103, 104, 159, 160, 179, 180, 192, 193, 202, 203], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 69, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 69, "excluded_lines": 0}, "missing_lines": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 27, 36, 38, 39, 46, 48, 49, 50, 53, 54, 55, 56, 59, 60, 61, 62, 64, 67, 68, 69, 71, 79, 88, 89, 95, 96, 99, 100, 103, 104, 117, 119, 120, 122, 127, 129, 132, 133, 134, 143, 150, 152, 153, 159, 160, 173, 174, 179, 180, 189, 192, 193, 199, 202, 203, 209], "excluded_lines": []}}}, "app/models/llm.py": {"executed_lines": [1, 2, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32], "summary": {"covered_lines": 27, "num_statements": 27, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 2, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32], "summary": {"covered_lines": 27, "num_statements": 27, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"LLM": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 32], "summary": {"covered_lines": 27, "num_statements": 27, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/models/user.py": {"executed_lines": [1, 13, 14, 15, 17, 18, 24, 25, 26, 27, 28, 30, 31, 37, 39, 40, 46, 47, 48, 49, 51, 52, 58, 59, 60, 62], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 13, 14, 15, 17, 18, 24, 25, 26, 27, 28, 30, 31, 37, 39, 40, 46, 47, 48, 49, 51, 52, 58, 59, 60, 62], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"UserBase": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UserCreate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UserInDB": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "User": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 13, 14, 15, 17, 18, 24, 25, 26, 27, 28, 30, 31, 37, 39, 40, 46, 47, 48, 49, 51, 52, 58, 59, 60, 62], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/__init__.py": {"executed_lines": [1, 4, 5, 6, 7, 8, 9, 10, 11, 18, 19, 20, 21, 22, 23, 24, 27, 31, 34, 35, 36, 37, 38, 39, 40, 43, 46, 49, 52, 54, 151], "summary": {"covered_lines": 30, "num_statements": 90, "percent_covered": 33.333333333333336, "percent_covered_display": "33", "missing_lines": 60, "excluded_lines": 0}, "missing_lines": [58, 59, 60, 61, 62, 63, 66, 67, 73, 74, 75, 76, 77, 80, 81, 82, 83, 84, 85, 87, 88, 89, 90, 91, 93, 94, 97, 98, 99, 100, 101, 104, 105, 107, 114, 115, 116, 117, 118, 119, 121, 122, 125, 126, 127, 128, 129, 132, 133, 134, 135, 136, 139, 140, 141, 142, 143, 146, 147, 149], "excluded_lines": [], "functions": {"initialize_rag": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 60, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 60, "excluded_lines": 0}, "missing_lines": [58, 59, 60, 61, 62, 63, 66, 67, 73, 74, 75, 76, 77, 80, 81, 82, 83, 84, 85, 87, 88, 89, 90, 91, 93, 94, 97, 98, 99, 100, 101, 104, 105, 107, 114, 115, 116, 117, 118, 119, 121, 122, 125, 126, 127, 128, 129, 132, 133, 134, 135, 136, 139, 140, 141, 142, 143, 146, 147, 149], "excluded_lines": []}, "": {"executed_lines": [1, 4, 5, 6, 7, 8, 9, 10, 11, 18, 19, 20, 21, 22, 23, 24, 27, 31, 34, 35, 36, 37, 38, 39, 40, 43, 46, 49, 52, 54, 151], "summary": {"covered_lines": 30, "num_statements": 30, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 4, 5, 6, 7, 8, 9, 10, 11, 18, 19, 20, 21, 22, 23, 24, 27, 31, 34, 35, 36, 37, 38, 39, 40, 43, 46, 49, 52, 54, 151], "summary": {"covered_lines": 30, "num_statements": 90, "percent_covered": 33.333333333333336, "percent_covered_display": "33", "missing_lines": 60, "excluded_lines": 0}, "missing_lines": [58, 59, 60, 61, 62, 63, 66, 67, 73, 74, 75, 76, 77, 80, 81, 82, 83, 84, 85, 87, 88, 89, 90, 91, 93, 94, 97, 98, 99, 100, 101, 104, 105, 107, 114, 115, 116, 117, 118, 119, 121, 122, 125, 126, 127, 128, 129, 132, 133, 134, 135, 136, 139, 140, 141, 142, 143, 146, 147, 149], "excluded_lines": []}}}, "app/rag/cache_service.py": {"executed_lines": [1, 2, 3, 4, 5, 6, 8, 10, 11, 46, 64, 91, 105], "summary": {"covered_lines": 13, "num_statements": 66, "percent_covered": 19.696969696969695, "percent_covered_display": "20", "missing_lines": 53, "excluded_lines": 0}, "missing_lines": [19, 20, 28, 29, 35, 36, 39, 40, 41, 42, 43, 44, 48, 49, 51, 52, 53, 55, 57, 58, 59, 60, 61, 62, 66, 67, 69, 70, 73, 82, 87, 88, 89, 93, 94, 96, 97, 98, 99, 100, 101, 102, 103, 107, 108, 110, 111, 112, 113, 114, 115, 116, 117], "excluded_lines": [], "functions": {"CacheService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [19, 20, 28, 29, 35, 36, 39, 40, 41, 42, 43, 44], "excluded_lines": []}, "CacheService.get_cached_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [48, 49, 51, 52, 53, 55, 57, 58, 59, 60, 61, 62], "excluded_lines": []}, "CacheService.cache_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [66, 67, 69, 70, 73, 82, 87, 88, 89], "excluded_lines": []}, "CacheService.invalidate_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [93, 94, 96, 97, 98, 99, 100, 101, 102, 103], "excluded_lines": []}, "CacheService.clear_all_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [107, 108, 110, 111, 112, 113, 114, 115, 116, 117], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 8, 10, 11, 46, 64, 91, 105], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CacheService": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 53, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 53, "excluded_lines": 0}, "missing_lines": [19, 20, 28, 29, 35, 36, 39, 40, 41, 42, 43, 44, 48, 49, 51, 52, 53, 55, 57, 58, 59, 60, 61, 62, 66, 67, 69, 70, 73, 82, 87, 88, 89, 93, 94, 96, 97, 98, 99, 100, 101, 102, 103, 107, 108, 110, 111, 112, 113, 114, 115, 116, 117], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 8, 10, 11, 46, 64, 91, 105], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/cleaner/clean_processor.py": {"executed_lines": [1, 3, 4, 5, 6, 8, 10, 11, 12, 13, 14, 16, 17, 19, 20, 51, 52, 56, 57, 61, 62, 79, 80, 90, 91, 111, 112], "summary": {"covered_lines": 24, "num_statements": 64, "percent_covered": 37.5, "percent_covered_display": "38", "missing_lines": 40, "excluded_lines": 0}, "missing_lines": [28, 29, 32, 34, 35, 38, 40, 41, 44, 47, 49, 54, 59, 65, 66, 69, 72, 75, 77, 83, 86, 88, 93, 94, 95, 97, 98, 101, 102, 105, 107, 109, 115, 116, 119, 120, 123, 124, 127, 129], "excluded_lines": [], "functions": {"CleanProcessor.clean": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [28, 29, 32, 34, 35, 38, 40, 41, 44, 47, 49], "excluded_lines": []}, "CleanProcessor.clean_basic": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [54], "excluded_lines": []}, "CleanProcessor.clean_normal": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [59], "excluded_lines": []}, "CleanProcessor._remove_invalid_chars": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [65, 66, 69, 72, 75, 77], "excluded_lines": []}, "CleanProcessor._normalize_whitespace": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [83, 86, 88], "excluded_lines": []}, "CleanProcessor._process_lines": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [93, 94, 95, 97, 98, 101, 102, 105, 107, 109], "excluded_lines": []}, "CleanProcessor._normalize_punctuation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [115, 116, 119, 120, 123, 124, 127, 129], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 5, 6, 8, 10, 11, 12, 13, 14, 16, 17, 19, 20, 51, 52, 56, 57, 61, 62, 79, 80, 90, 91, 111, 112], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CleanLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CleanProcessor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 40, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 40, "excluded_lines": 0}, "missing_lines": [28, 29, 32, 34, 35, 38, 40, 41, 44, 47, 49, 54, 59, 65, 66, 69, 72, 75, 77, 83, 86, 88, 93, 94, 95, 97, 98, 101, 102, 105, 107, 109, 115, 116, 119, 120, 123, 124, 127, 129], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 5, 6, 8, 10, 11, 12, 13, 14, 16, 17, 19, 20, 51, 52, 56, 57, 61, 62, 79, 80, 90, 91, 111, 112], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/collection_manager.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 137, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 137, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 15, 16, 18, 21, 24, 32, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 48, 50, 51, 53, 54, 55, 57, 58, 59, 60, 61, 63, 73, 120, 126, 128, 143, 144, 146, 148, 149, 150, 151, 153, 154, 155, 158, 159, 160, 163, 164, 165, 166, 168, 170, 172, 173, 174, 176, 190, 191, 192, 195, 198, 199, 200, 201, 203, 206, 207, 213, 214, 222, 223, 224, 226, 227, 229, 230, 231, 233, 243, 244, 246, 247, 248, 250, 251, 253, 264, 265, 272, 273, 274, 275, 277, 280, 281, 287, 289, 290, 291, 293, 300, 301, 303, 304, 305, 306, 307, 309, 323, 324, 326, 327, 330, 331, 332, 335, 336, 339, 340, 341, 343, 346, 352, 353, 356, 361, 362, 364, 366, 367, 368, 372], "excluded_lines": [], "functions": {"MilvusCollectionManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [32, 33, 34], "excluded_lines": []}, "MilvusCollectionManager.connect": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [38, 39, 40, 41, 42, 43, 44, 45, 46], "excluded_lines": []}, "MilvusCollectionManager.disconnect": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [50, 51, 53, 54, 55, 57, 58, 59, 60, 61], "excluded_lines": []}, "MilvusCollectionManager.create_standard_schema": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [73, 120, 126], "excluded_lines": []}, "MilvusCollectionManager.create_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [143, 144, 146, 148, 149, 150, 151, 153, 154, 155, 158, 159, 160, 163, 164, 165, 166, 168, 170, 172, 173, 174], "excluded_lines": []}, "MilvusCollectionManager.create_indexes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [190, 191, 192, 195, 198, 199, 200, 201, 203, 206, 207, 213, 214, 222, 223, 224, 226, 227, 229, 230, 231], "excluded_lines": []}, "MilvusCollectionManager.get_collection_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [243, 244, 246, 247, 248, 250, 251, 253, 264, 265, 272, 273, 274, 275, 277, 280, 281, 287, 289, 290, 291], "excluded_lines": []}, "MilvusCollectionManager.list_collections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [300, 301, 303, 304, 305, 306, 307], "excluded_lines": []}, "MilvusCollectionManager.migrate_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [323, 324, 326, 327, 330, 331, 332, 335, 336, 339, 340, 341, 343, 346, 352, 353, 356, 361, 362, 364, 366, 367, 368], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 15, 16, 18, 21, 24, 36, 48, 63, 128, 176, 233, 293, 309, 372], "excluded_lines": []}}, "classes": {"MilvusCollectionManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 119, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 119, "excluded_lines": 0}, "missing_lines": [32, 33, 34, 38, 39, 40, 41, 42, 43, 44, 45, 46, 50, 51, 53, 54, 55, 57, 58, 59, 60, 61, 73, 120, 126, 143, 144, 146, 148, 149, 150, 151, 153, 154, 155, 158, 159, 160, 163, 164, 165, 166, 168, 170, 172, 173, 174, 190, 191, 192, 195, 198, 199, 200, 201, 203, 206, 207, 213, 214, 222, 223, 224, 226, 227, 229, 230, 231, 243, 244, 246, 247, 248, 250, 251, 253, 264, 265, 272, 273, 274, 275, 277, 280, 281, 287, 289, 290, 291, 300, 301, 303, 304, 305, 306, 307, 323, 324, 326, 327, 330, 331, 332, 335, 336, 339, 340, 341, 343, 346, 352, 353, 356, 361, 362, 364, 366, 367, 368], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [7, 8, 9, 10, 15, 16, 18, 21, 24, 36, 48, 63, 128, 176, 233, 293, 309, 372], "excluded_lines": []}}}, "app/rag/config.py": {"executed_lines": [1, 4, 5, 8, 16, 25, 41], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 4, 5, 8, 16, 25, 41], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 4, 5, 8, 16, 25, 41], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/constants.py": {"executed_lines": [1, 5, 6, 12, 15, 28, 56, 65, 72, 88, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 109, 110, 111, 112, 113, 114, 115, 116, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128], "summary": {"covered_lines": 35, "num_statements": 35, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 5, 6, 12, 15, 28, 56, 65, 72, 88, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 109, 110, 111, 112, 113, 114, 115, 116, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128], "summary": {"covered_lines": 35, "num_statements": 35, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"Field": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Distance": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "IndexType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 5, 6, 12, 15, 28, 56, 65, 72, 88, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 109, 110, 111, 112, 113, 114, 115, 116, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128], "summary": {"covered_lines": 35, "num_statements": 35, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/custom_exceptions.py": {"executed_lines": [1, 7, 8, 9, 12, 13, 14, 16, 17, 18, 20, 21, 22, 24, 25, 26, 28, 29, 30, 32, 33, 34, 37, 38, 39, 41, 42, 43, 45, 46, 47, 49, 50, 51, 53, 54, 55, 57, 58, 59, 61, 62, 63, 65, 66, 67, 70, 71, 72, 74, 75, 76, 78, 79, 80, 82, 83, 84, 87, 88, 89, 91, 92, 93, 96, 97, 98, 100, 101, 102], "summary": {"covered_lines": 46, "num_statements": 46, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 7, 8, 9, 12, 13, 14, 16, 17, 18, 20, 21, 22, 24, 25, 26, 28, 29, 30, 32, 33, 34, 37, 38, 39, 41, 42, 43, 45, 46, 47, 49, 50, 51, 53, 54, 55, 57, 58, 59, 61, 62, 63, 65, 66, 67, 70, 71, 72, 74, 75, 76, 78, 79, 80, 82, 83, 84, 87, 88, 89, 91, 92, 93, 96, 97, 98, 100, 101, 102], "summary": {"covered_lines": 46, "num_statements": 46, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"RAGBaseException": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentProcessError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentValidationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentCleaningError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentSplitError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FileProcessError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PDFProcessError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "VectorStoreError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CollectionCreateError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CollectionLoadError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "IndexCreateError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SearchError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InsertError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DeleteError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EmbeddingError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelConnectionError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EmbeddingTimeoutError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EmbeddingBatchError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RetrievalError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RerankingError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RedisConnectionError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 7, 8, 9, 12, 13, 14, 16, 17, 18, 20, 21, 22, 24, 25, 26, 28, 29, 30, 32, 33, 34, 37, 38, 39, 41, 42, 43, 45, 46, 47, 49, 50, 51, 53, 54, 55, 57, 58, 59, 61, 62, 63, 65, 66, 67, 70, 71, 72, 74, 75, 76, 78, 79, 80, 82, 83, 84, 87, 88, 89, 91, 92, 93, 96, 97, 98, 100, 101, 102], "summary": {"covered_lines": 46, "num_statements": 46, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/database.py": {"executed_lines": [1, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 17, 18, 20, 43, 59, 84, 110, 128, 147, 156, 165, 173, 211, 230, 250, 265, 280, 295, 310], "summary": {"covered_lines": 28, "num_statements": 140, "percent_covered": 20.0, "percent_covered_display": "20", "missing_lines": 112, "excluded_lines": 0}, "missing_lines": [21, 22, 23, 24, 27, 28, 29, 32, 33, 36, 38, 39, 40, 41, 45, 47, 48, 49, 52, 53, 56, 57, 61, 63, 73, 79, 80, 81, 82, 86, 88, 99, 105, 106, 107, 108, 112, 113, 114, 115, 123, 124, 125, 126, 130, 131, 132, 143, 144, 145, 149, 150, 151, 152, 153, 154, 158, 159, 160, 161, 162, 163, 167, 168, 169, 170, 171, 175, 177, 180, 191, 192, 198, 200, 201, 202, 204, 205, 207, 208, 209, 213, 214, 215, 223, 225, 226, 228, 232, 233, 234, 243, 245, 246, 248, 252, 253, 254, 256, 267, 268, 269, 271, 282, 283, 297, 307, 308, 312, 313, 314, 316], "excluded_lines": [], "functions": {"MongoDBManager.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [21, 22, 23, 24, 27, 28, 29, 32, 33, 36, 38, 39, 40, 41], "excluded_lines": []}, "MongoDBManager._create_indexes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [45, 47, 48, 49, 52, 53, 56, 57], "excluded_lines": []}, "MongoDBManager.save_document_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [61, 63, 73, 79, 80, 81, 82], "excluded_lines": []}, "MongoDBManager.save_child_chunk": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [86, 88, 99, 105, 106, 107, 108], "excluded_lines": []}, "MongoDBManager.get_document_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [112, 113, 114, 115, 123, 124, 125, 126], "excluded_lines": []}, "MongoDBManager.get_child_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [130, 131, 132, 143, 144, 145], "excluded_lines": []}, "MongoDBManager.delete_document_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [149, 150, 151, 152, 153, 154], "excluded_lines": []}, "MongoDBManager.delete_child_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [158, 159, 160, 161, 162, 163], "excluded_lines": []}, "MongoDBManager.close": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [167, 168, 169, 170, 171], "excluded_lines": []}, "MongoDBManager.save_document": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [175, 177, 180, 191, 192, 198, 200, 201, 202, 204, 205, 207, 208, 209], "excluded_lines": []}, "MongoDBManager.insert_segments": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [213, 214, 215, 223, 225, 226, 228], "excluded_lines": []}, "MongoDBManager.insert_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [232, 233, 234, 243, 245, 246, 248], "excluded_lines": []}, "MongoDBManager.get_segment_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [252, 253, 254, 256], "excluded_lines": []}, "MongoDBManager.get_chunk_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [267, 268, 269, 271], "excluded_lines": []}, "MongoDBManager.get_chunks_by_segment_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [282, 283], "excluded_lines": []}, "MongoDBManager.insert_document": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [297, 307, 308], "excluded_lines": []}, "MongoDBManager.get_document_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [312, 313, 314, 316], "excluded_lines": []}, "": {"executed_lines": [1, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 17, 18, 20, 43, 59, 84, 110, 128, 147, 156, 165, 173, 211, 230, 250, 265, 280, 295, 310], "summary": {"covered_lines": 28, "num_statements": 28, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"MongoDBManager": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 112, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 112, "excluded_lines": 0}, "missing_lines": [21, 22, 23, 24, 27, 28, 29, 32, 33, 36, 38, 39, 40, 41, 45, 47, 48, 49, 52, 53, 56, 57, 61, 63, 73, 79, 80, 81, 82, 86, 88, 99, 105, 106, 107, 108, 112, 113, 114, 115, 123, 124, 125, 126, 130, 131, 132, 143, 144, 145, 149, 150, 151, 152, 153, 154, 158, 159, 160, 161, 162, 163, 167, 168, 169, 170, 171, 175, 177, 180, 191, 192, 198, 200, 201, 202, 204, 205, 207, 208, 209, 213, 214, 215, 223, 225, 226, 228, 232, 233, 234, 243, 245, 246, 248, 252, 253, 254, 256, 267, 268, 269, 271, 282, 283, 297, 307, 308, 312, 313, 314, 316], "excluded_lines": []}, "": {"executed_lines": [1, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 17, 18, 20, 43, 59, 84, 110, 128, 147, 156, 165, 173, 211, 230, 250, 265, 280, 295, 310], "summary": {"covered_lines": 28, "num_statements": 28, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/document_processor.py": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 14, 16, 18, 19, 22, 40, 56, 85, 144, 177, 184, 188, 204], "summary": {"covered_lines": 22, "num_statements": 101, "percent_covered": 21.782178217821784, "percent_covered_display": "22", "missing_lines": 79, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 27, 28, 31, 32, 35, 36, 38, 50, 51, 52, 54, 67, 68, 69, 72, 73, 74, 77, 78, 79, 80, 81, 83, 96, 97, 98, 99, 100, 101, 104, 107, 110, 114, 117, 120, 121, 122, 125, 126, 127, 130, 136, 139, 140, 142, 156, 159, 162, 165, 168, 169, 170, 173, 175, 180, 181, 182, 186, 190, 191, 192, 193, 194, 195, 199, 200, 201, 202, 206, 207, 208, 209, 213, 214, 215, 216], "excluded_lines": [], "functions": {"DocumentProcessor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 27, 28, 31, 32, 35, 36, 38], "excluded_lines": []}, "DocumentProcessor.process_document": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [50, 51, 52, 54], "excluded_lines": []}, "DocumentProcessor.validate_document": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [67, 68, 69, 72, 73, 74, 77, 78, 79, 80, 81, 83], "excluded_lines": []}, "DocumentProcessor.clean_document": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [96, 97, 98, 99, 100, 101, 104, 107, 110, 114, 117, 120, 121, 122, 125, 126, 127, 130, 136, 139, 140, 142], "excluded_lines": []}, "DocumentProcessor.extract_keywords": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [156, 159, 162, 165, 168, 169, 170, 173, 175], "excluded_lines": []}, "DocumentProcessor._get_cache_key": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [180, 181, 182], "excluded_lines": []}, "DocumentProcessor._get_cache_path": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [186], "excluded_lines": []}, "DocumentProcessor._get_from_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [190, 191, 192, 193, 194, 195, 199, 200, 201, 202], "excluded_lines": []}, "DocumentProcessor._save_to_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [206, 207, 208, 209, 213, 214, 215, 216], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 14, 16, 18, 19, 22, 40, 56, 85, 144, 177, 184, 188, 204], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DocumentProcessor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 79, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 79, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 27, 28, 31, 32, 35, 36, 38, 50, 51, 52, 54, 67, 68, 69, 72, 73, 74, 77, 78, 79, 80, 81, 83, 96, 97, 98, 99, 100, 101, 104, 107, 110, 114, 117, 120, 121, 122, 125, 126, 127, 130, 136, 139, 140, 142, 156, 159, 162, 165, 168, 169, 170, 173, 175, 180, 181, 182, 186, 190, 191, 192, 193, 194, 195, 199, 200, 201, 202, 206, 207, 208, 209, 213, 214, 215, 216], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 14, 16, 18, 19, 22, 40, 56, 85, 144, 177, 184, 188, 204], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/document_splitter.py": {"executed_lines": [1, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 21, 22, 23, 24, 25, 27, 28, 29, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 55, 56, 57, 65, 66, 67, 68, 69, 72, 81, 83, 105, 120, 189, 207, 272, 273, 274, 312, 313, 314, 316, 319, 321, 323, 324, 327, 337, 339, 342, 343, 344, 348, 349, 352, 355, 356, 366, 372, 376, 378, 382, 392, 393, 396, 397, 398, 399, 403, 404, 407, 408, 411, 412, 425, 430, 431, 434, 437, 438, 440, 441], "summary": {"covered_lines": 89, "num_statements": 179, "percent_covered": 49.720670391061454, "percent_covered_display": "50", "missing_lines": 90, "excluded_lines": 0}, "missing_lines": [86, 103, 108, 118, 123, 126, 127, 130, 132, 134, 135, 136, 137, 140, 142, 143, 144, 145, 148, 150, 151, 152, 154, 155, 158, 160, 161, 162, 163, 164, 165, 168, 169, 171, 172, 173, 177, 178, 179, 181, 184, 185, 187, 192, 193, 194, 195, 198, 199, 202, 203, 205, 209, 210, 212, 214, 216, 219, 221, 224, 225, 236, 239, 240, 245, 247, 248, 251, 252, 254, 268, 270, 276, 277, 279, 281, 283, 284, 286, 287, 288, 291, 294, 295, 308, 310, 317, 345, 380, 400], "excluded_lines": [], "functions": {"Rule.__init__": {"executed_lines": [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentSplitter.__init__": {"executed_lines": [65, 66, 67, 68, 69, 72, 81], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentSplitter._is_title": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [86, 103], "excluded_lines": []}, "DocumentSplitter._is_list_item": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [108, 118], "excluded_lines": []}, "DocumentSplitter._split_into_paragraphs": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 39, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 39, "excluded_lines": 0}, "missing_lines": [123, 126, 127, 130, 132, 134, 135, 136, 137, 140, 142, 143, 144, 145, 148, 150, 151, 152, 154, 155, 158, 160, 161, 162, 163, 164, 165, 168, 169, 171, 172, 173, 177, 178, 179, 181, 184, 185, 187], "excluded_lines": []}, "DocumentSplitter._is_new_topic": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [192, 193, 194, 195, 198, 199, 202, 203, 205], "excluded_lines": []}, "DocumentSplitter.split_documents": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [209, 210, 212, 214, 216, 219, 221, 224, 225, 236, 239, 240, 245, 247, 248, 251, 252, 254, 268, 270], "excluded_lines": []}, "QADocumentSplitter.split_documents": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [276, 277, 279, 281, 283, 284, 286, 287, 288, 291, 294, 295, 308, 310], "excluded_lines": []}, "ParentChildDocumentSplitter.split_documents": {"executed_lines": [316, 319, 321, 323, 324, 327, 337, 339, 342, 343, 344, 348, 349, 352, 355, 356, 366, 372, 376, 378, 382, 392, 393, 396, 397, 398, 399, 403, 404, 407, 408, 411, 412, 425, 430, 431, 434, 437, 438, 440, 441], "summary": {"covered_lines": 41, "num_statements": 45, "percent_covered": 91.11111111111111, "percent_covered_display": "91", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [317, 345, 380, 400], "excluded_lines": []}, "": {"executed_lines": [1, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 21, 22, 23, 24, 25, 27, 28, 29, 55, 56, 57, 83, 105, 120, 189, 207, 272, 273, 274, 312, 313, 314], "summary": {"covered_lines": 30, "num_statements": 30, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SplitMode": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Rule": {"executed_lines": [43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentSplitter": {"executed_lines": [65, 66, 67, 68, 69, 72, 81], "summary": {"covered_lines": 7, "num_statements": 79, "percent_covered": 8.860759493670885, "percent_covered_display": "9", "missing_lines": 72, "excluded_lines": 0}, "missing_lines": [86, 103, 108, 118, 123, 126, 127, 130, 132, 134, 135, 136, 137, 140, 142, 143, 144, 145, 148, 150, 151, 152, 154, 155, 158, 160, 161, 162, 163, 164, 165, 168, 169, 171, 172, 173, 177, 178, 179, 181, 184, 185, 187, 192, 193, 194, 195, 198, 199, 202, 203, 205, 209, 210, 212, 214, 216, 219, 221, 224, 225, 236, 239, 240, 245, 247, 248, 251, 252, 254, 268, 270], "excluded_lines": []}, "QADocumentSplitter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [276, 277, 279, 281, 283, 284, 286, 287, 288, 291, 294, 295, 308, 310], "excluded_lines": []}, "ParentChildDocumentSplitter": {"executed_lines": [316, 319, 321, 323, 324, 327, 337, 339, 342, 343, 344, 348, 349, 352, 355, 356, 366, 372, 376, 378, 382, 392, 393, 396, 397, 398, 399, 403, 404, 407, 408, 411, 412, 425, 430, 431, 434, 437, 438, 440, 441], "summary": {"covered_lines": 41, "num_statements": 45, "percent_covered": 91.11111111111111, "percent_covered_display": "91", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [317, 345, 380, 400], "excluded_lines": []}, "": {"executed_lines": [1, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 17, 19, 21, 22, 23, 24, 25, 27, 28, 29, 55, 56, 57, 83, 105, 120, 189, 207, 272, 273, 274, 312, 313, 314], "summary": {"covered_lines": 30, "num_statements": 30, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/document_store.py": {"executed_lines": [1, 2, 3, 4, 6, 8, 9, 11, 16, 26, 43, 54, 66, 78], "summary": {"covered_lines": 13, "num_statements": 63, "percent_covered": 20.634920634920636, "percent_covered_display": "21", "missing_lines": 50, "excluded_lines": 0}, "missing_lines": [12, 13, 14, 18, 19, 20, 21, 22, 23, 24, 28, 29, 30, 33, 34, 36, 37, 39, 40, 41, 45, 46, 47, 48, 49, 50, 51, 52, 56, 57, 58, 59, 60, 61, 62, 63, 64, 68, 70, 72, 73, 74, 75, 76, 80, 81, 85, 86, 87, 88], "excluded_lines": [], "functions": {"DocumentStore.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [12, 13, 14], "excluded_lines": []}, "DocumentStore.store_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [18, 19, 20, 21, 22, 23, 24], "excluded_lines": []}, "DocumentStore.store_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [28, 29, 30, 33, 34, 36, 37, 39, 40, 41], "excluded_lines": []}, "DocumentStore.get_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [45, 46, 47, 48, 49, 50, 51, 52], "excluded_lines": []}, "DocumentStore.get_chunks_by_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [56, 57, 58, 59, 60, 61, 62, 63, 64], "excluded_lines": []}, "DocumentStore.delete_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [68, 70, 72, 73, 74, 75, 76], "excluded_lines": []}, "DocumentStore.update_segment": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [80, 81, 85, 86, 87, 88], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 6, 8, 9, 11, 16, 26, 43, 54, 66, 78], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DocumentStore": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 50, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 50, "excluded_lines": 0}, "missing_lines": [12, 13, 14, 18, 19, 20, 21, 22, 23, 24, 28, 29, 30, 33, 34, 36, 37, 39, 40, 41, 45, 46, 47, 48, 49, 50, 51, 52, 56, 57, 58, 59, 60, 61, 62, 63, 64, 68, 70, 72, 73, 74, 75, 76, 80, 81, 85, 86, 87, 88], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 6, 8, 9, 11, 16, 26, 43, 54, 66, 78], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/embedding_model.py": {"executed_lines": [1, 5, 6, 7, 8, 9, 10, 11, 13, 15, 16, 18, 36, 76, 115, 140, 170], "summary": {"covered_lines": 15, "num_statements": 111, "percent_covered": 13.513513513513514, "percent_covered_display": "14", "missing_lines": 96, "excluded_lines": 0}, "missing_lines": [20, 21, 24, 25, 26, 27, 30, 31, 33, 34, 38, 40, 41, 42, 43, 45, 46, 49, 50, 51, 53, 54, 57, 58, 59, 62, 63, 64, 65, 66, 67, 69, 71, 72, 73, 74, 78, 80, 81, 83, 89, 96, 97, 98, 99, 100, 103, 105, 106, 107, 108, 109, 111, 113, 117, 119, 127, 128, 129, 131, 132, 133, 134, 135, 136, 137, 138, 142, 143, 145, 147, 151, 152, 154, 155, 163, 164, 165, 167, 168, 173, 174, 176, 178, 180, 181, 182, 185, 186, 187, 188, 189, 190, 192, 193, 194], "excluded_lines": [], "functions": {"EmbeddingModel.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [20, 21, 24, 25, 26, 27, 30, 31, 33, 34], "excluded_lines": []}, "EmbeddingModel.embed_documents": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 26, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 26, "excluded_lines": 0}, "missing_lines": [38, 40, 41, 42, 43, 45, 46, 49, 50, 51, 53, 54, 57, 58, 59, 62, 63, 64, 65, 66, 67, 69, 71, 72, 73, 74], "excluded_lines": []}, "EmbeddingModel._embed_batch_with_retry": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [78, 80, 81, 83, 89, 96, 97, 98, 99, 100, 103, 105, 106, 107, 108, 109, 111, 113], "excluded_lines": []}, "EmbeddingModel.embed_query": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [117, 119, 127, 128, 129, 131, 132, 133, 134, 135, 136, 137, 138], "excluded_lines": []}, "EmbeddingModel.check_api_availability": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [142, 143, 145, 147, 151, 152, 154, 155, 163, 164, 165, 167, 168], "excluded_lines": []}, "EmbeddingModel.get_dimension": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [173, 174, 176, 178, 180, 181, 182, 185, 186, 187, 188, 189, 190, 192, 193, 194], "excluded_lines": []}, "": {"executed_lines": [1, 5, 6, 7, 8, 9, 10, 11, 13, 15, 16, 18, 36, 76, 115, 140, 170], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"EmbeddingModel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 96, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 96, "excluded_lines": 0}, "missing_lines": [20, 21, 24, 25, 26, 27, 30, 31, 33, 34, 38, 40, 41, 42, 43, 45, 46, 49, 50, 51, 53, 54, 57, 58, 59, 62, 63, 64, 65, 66, 67, 69, 71, 72, 73, 74, 78, 80, 81, 83, 89, 96, 97, 98, 99, 100, 103, 105, 106, 107, 108, 109, 111, 113, 117, 119, 127, 128, 129, 131, 132, 133, 134, 135, 136, 137, 138, 142, 143, 145, 147, 151, 152, 154, 155, 163, 164, 165, 167, 168, 173, 174, 176, 178, 180, 181, 182, 185, 186, 187, 188, 189, 190, 192, 193, 194], "excluded_lines": []}, "": {"executed_lines": [1, 5, 6, 7, 8, 9, 10, 11, 13, 15, 16, 18, 36, 76, 115, 140, 170], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/models.py": {"executed_lines": [1, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 32, 34, 35, 37, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 60, 61, 62, 63, 65, 67, 68, 70, 89, 103, 104, 105, 106, 107, 109, 114, 124, 125, 126, 127, 128, 129, 130, 131, 132, 134], "summary": {"covered_lines": 61, "num_statements": 75, "percent_covered": 81.33333333333333, "percent_covered_display": "81", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [30, 39, 72, 84, 85, 87, 91, 110, 111, 112, 116, 117, 122, 136], "excluded_lines": [], "functions": {"Document.__init__": {"executed_lines": [24, 25, 26, 27, 28, 29], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [30], "excluded_lines": []}, "Document._generate_hash": {"executed_lines": [34, 35], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Document.to_point_struct": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [39], "excluded_lines": []}, "DocumentSegment.__init__": {"executed_lines": [61, 62, 63], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentSegment._generate_hash": {"executed_lines": [67, 68], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DocumentSegment.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [72, 84, 85, 87], "excluded_lines": []}, "DocumentSegment.to_point_struct": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [91], "excluded_lines": []}, "ChildDocument.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [110, 111, 112], "excluded_lines": []}, "ChildDocument.to_point_struct": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [116, 117, 122], "excluded_lines": []}, "ChildChunk.to_point_struct": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [136], "excluded_lines": []}, "": {"executed_lines": [1, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 32, 37, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 60, 65, 70, 89, 103, 104, 105, 106, 107, 109, 114, 124, 125, 126, 127, 128, 129, 130, 131, 132, 134], "summary": {"covered_lines": 48, "num_statements": 48, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"Document": {"executed_lines": [24, 25, 26, 27, 28, 29, 34, 35], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [30, 39], "excluded_lines": []}, "DocumentSegment": {"executed_lines": [61, 62, 63, 67, 68], "summary": {"covered_lines": 5, "num_statements": 10, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [72, 84, 85, 87, 91], "excluded_lines": []}, "ChildDocument": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [110, 111, 112, 116, 117, 122], "excluded_lines": []}, "ChildChunk": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [136], "excluded_lines": []}, "": {"executed_lines": [1, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 32, 37, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 60, 65, 70, 89, 103, 104, 105, 106, 107, 109, 114, 124, 125, 126, 127, 128, 129, 130, 131, 132, 134], "summary": {"covered_lines": 48, "num_statements": 48, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/parent_child_processor.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 117, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 117, "excluded_lines": 0}, "missing_lines": [1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 16, 18, 19, 20, 22, 24, 25, 26, 28, 30, 32, 35, 36, 38, 40, 41, 42, 43, 45, 46, 48, 50, 52, 57, 61, 62, 63, 68, 71, 72, 74, 75, 76, 77, 79, 82, 83, 85, 86, 87, 90, 91, 98, 99, 100, 102, 105, 106, 107, 108, 110, 113, 116, 117, 119, 120, 121, 123, 125, 127, 128, 131, 132, 133, 135, 137, 138, 139, 141, 147, 148, 149, 151, 152, 153, 156, 157, 158, 166, 167, 168, 171, 177, 179, 181, 182, 183, 185, 187, 188, 189, 191, 192, 193, 194, 195, 197, 199, 200, 201, 203, 204, 205, 206, 207, 208], "excluded_lines": [], "functions": {"ParentChildIndexProcessor.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [36], "excluded_lines": []}, "ParentChildIndexProcessor.transform": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 54, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 54, "excluded_lines": 0}, "missing_lines": [40, 41, 42, 43, 45, 46, 48, 50, 52, 57, 61, 62, 63, 68, 71, 72, 74, 75, 76, 77, 79, 82, 83, 85, 86, 87, 90, 91, 98, 99, 100, 102, 105, 106, 107, 108, 110, 113, 116, 117, 119, 120, 121, 123, 125, 127, 128, 131, 132, 133, 135, 137, 138, 139], "excluded_lines": []}, "ParentChildIndexProcessor._split_child_documents": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [147, 148, 149, 151, 152, 153, 156, 157, 158, 166, 167, 168, 171, 177, 179, 181, 182, 183], "excluded_lines": []}, "ParentChildIndexProcessor._clean_text": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [187, 188, 189, 191, 192, 193, 194, 195], "excluded_lines": []}, "ParentChildIndexProcessor._clean_separators": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [199, 200, 201, 203, 204, 205, 206, 207, 208], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 16, 18, 19, 20, 22, 24, 25, 26, 28, 30, 32, 35, 38, 141, 185, 197], "excluded_lines": []}}, "classes": {"ParentMode": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Segmentation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProcessingRule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProcessingError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ParentChildIndexProcessor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 90, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 90, "excluded_lines": 0}, "missing_lines": [36, 40, 41, 42, 43, 45, 46, 48, 50, 52, 57, 61, 62, 63, 68, 71, 72, 74, 75, 76, 77, 79, 82, 83, 85, 86, 87, 90, 91, 98, 99, 100, 102, 105, 106, 107, 108, 110, 113, 116, 117, 119, 120, 121, 123, 125, 127, 128, 131, 132, 133, 135, 137, 138, 139, 147, 148, 149, 151, 152, 153, 156, 157, 158, 166, 167, 168, 171, 177, 179, 181, 182, 183, 187, 188, 189, 191, 192, 193, 194, 195, 199, 200, 201, 203, 204, 205, 206, 207, 208], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [1, 2, 3, 4, 5, 6, 7, 8, 10, 12, 14, 16, 18, 19, 20, 22, 24, 25, 26, 28, 30, 32, 35, 38, 141, 185, 197], "excluded_lines": []}}}, "app/rag/pdf_processor.py": {"executed_lines": [1, 3, 4, 5, 6, 7, 8, 10, 11, 12, 14, 45, 63, 92, 123, 125, 140], "summary": {"covered_lines": 16, "num_statements": 76, "percent_covered": 21.05263157894737, "percent_covered_display": "21", "missing_lines": 60, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 29, 30, 31, 34, 41, 42, 43, 47, 48, 49, 52, 57, 58, 59, 60, 61, 65, 67, 68, 70, 72, 73, 74, 75, 76, 79, 80, 81, 83, 86, 88, 89, 90, 94, 96, 97, 98, 100, 102, 103, 104, 105, 106, 109, 110, 111, 113, 116, 118, 119, 120, 136, 137, 138, 151, 152, 153], "excluded_lines": [], "functions": {"PDFProcessor.__init__": {"executed_lines": [12], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "PDFProcessor.process_pdf": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 29, 30, 31, 34, 41, 42, 43], "excluded_lines": []}, "PDFProcessor.process_pdf_bytes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [47, 48, 49, 52, 57, 58, 59, 60, 61], "excluded_lines": []}, "PDFProcessor._extract_text_from_pdf": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [65, 67, 68, 70, 72, 73, 74, 75, 76, 79, 80, 81, 83, 86, 88, 89, 90], "excluded_lines": []}, "PDFProcessor._extract_text_from_pdf_bytes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [94, 96, 97, 98, 100, 102, 103, 104, 105, 106, 109, 110, 111, 113, 116, 118, 119, 120], "excluded_lines": []}, "process_pdf": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [136, 137, 138], "excluded_lines": []}, "process_pdf_bytes": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [151, 152, 153], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 5, 6, 7, 8, 10, 11, 14, 45, 63, 92, 123, 125, 140], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"PDFProcessor": {"executed_lines": [12], "summary": {"covered_lines": 1, "num_statements": 55, "percent_covered": 1.8181818181818181, "percent_covered_display": "2", "missing_lines": 54, "excluded_lines": 0}, "missing_lines": [24, 25, 26, 29, 30, 31, 34, 41, 42, 43, 47, 48, 49, 52, 57, 58, 59, 60, 61, 65, 67, 68, 70, 72, 73, 74, 75, 76, 79, 80, 81, 83, 86, 88, 89, 90, 94, 96, 97, 98, 100, 102, 103, 104, 105, 106, 109, 110, 111, 113, 116, 118, 119, 120], "excluded_lines": []}, "": {"executed_lines": [1, 3, 4, 5, 6, 7, 8, 10, 11, 14, 45, 63, 92, 123, 125, 140], "summary": {"covered_lines": 15, "num_statements": 21, "percent_covered": 71.42857142857143, "percent_covered_display": "71", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [136, 137, 138, 151, 152, 153], "excluded_lines": []}}}, "app/rag/retrieval_service.py": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15, 18, 20, 21, 49, 152, 167, 238, 282, 310, 331, 372, 438], "summary": {"covered_lines": 24, "num_statements": 219, "percent_covered": 10.95890410958904, "percent_covered_display": "11", "missing_lines": 195, "excluded_lines": 0}, "missing_lines": [29, 30, 31, 32, 43, 46, 47, 70, 72, 73, 74, 75, 76, 77, 78, 79, 82, 83, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 100, 101, 106, 107, 108, 111, 112, 120, 121, 122, 125, 126, 127, 128, 129, 131, 132, 136, 137, 138, 139, 140, 143, 145, 147, 148, 149, 150, 154, 155, 156, 157, 158, 159, 160, 161, 162, 164, 165, 169, 171, 172, 176, 179, 180, 185, 186, 187, 189, 190, 191, 192, 197, 198, 199, 201, 204, 205, 206, 208, 209, 211, 212, 214, 215, 216, 217, 218, 221, 222, 225, 226, 228, 229, 231, 233, 234, 235, 236, 240, 241, 242, 245, 246, 249, 251, 254, 257, 258, 261, 262, 265, 268, 270, 275, 276, 277, 289, 291, 294, 297, 298, 299, 300, 301, 302, 304, 306, 307, 308, 312, 314, 318, 319, 321, 323, 325, 327, 328, 329, 346, 348, 351, 354, 357, 359, 365, 366, 367, 387, 388, 393, 394, 397, 400, 403, 406, 407, 408, 409, 412, 413, 414, 415, 417, 418, 421, 423, 431, 432, 433, 457, 459, 462, 463, 464, 465, 467, 472, 473, 480, 486, 488, 489, 490], "excluded_lines": [], "functions": {"RetrievalService.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [29, 30, 31, 32, 43, 46, 47], "excluded_lines": []}, "RetrievalService.retrieve": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 51, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 51, "excluded_lines": 0}, "missing_lines": [70, 72, 73, 74, 75, 76, 77, 78, 79, 82, 83, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 100, 101, 106, 107, 108, 111, 112, 120, 121, 122, 125, 126, 127, 128, 129, 131, 132, 136, 137, 138, 139, 140, 143, 145, 147, 148, 149, 150], "excluded_lines": []}, "RetrievalService._retry_operation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [154, 155, 156, 157, 158, 159, 160, 161, 162, 164, 165], "excluded_lines": []}, "RetrievalService._rerank_results": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 40, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 40, "excluded_lines": 0}, "missing_lines": [169, 171, 172, 176, 179, 180, 185, 186, 187, 189, 190, 191, 192, 197, 198, 199, 201, 204, 205, 206, 208, 209, 211, 212, 214, 215, 216, 217, 218, 221, 222, 225, 226, 228, 229, 231, 233, 234, 235, 236], "excluded_lines": []}, "RetrievalService.process_document": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [240, 241, 242, 245, 246, 249, 251, 254, 257, 258, 261, 262, 265, 268, 270, 275, 276, 277], "excluded_lines": []}, "RetrievalService.search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [289, 291, 294, 297, 298, 299, 300, 301, 302, 304, 306, 307, 308], "excluded_lines": []}, "RetrievalService.delete_document": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [312, 314, 318, 319, 321, 323, 325, 327, 328, 329], "excluded_lines": []}, "RetrievalService.process_and_index_document": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [346, 348, 351, 354, 357, 359, 365, 366, 367], "excluded_lines": []}, "RetrievalService.process_and_index_documents_batch": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 22, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [387, 388, 393, 394, 397, 400, 403, 406, 407, 408, 409, 412, 413, 414, 415, 417, 418, 421, 423, 431, 432, 433], "excluded_lines": []}, "RetrievalService.retrieve_with_parent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [457, 459, 462, 463, 464, 465, 467, 472, 473, 480, 486, 488, 489, 490], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15, 18, 20, 21, 49, 152, 167, 238, 282, 310, 331, 372, 438], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"RetrievalService": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 195, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 195, "excluded_lines": 0}, "missing_lines": [29, 30, 31, 32, 43, 46, 47, 70, 72, 73, 74, 75, 76, 77, 78, 79, 82, 83, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 100, 101, 106, 107, 108, 111, 112, 120, 121, 122, 125, 126, 127, 128, 129, 131, 132, 136, 137, 138, 139, 140, 143, 145, 147, 148, 149, 150, 154, 155, 156, 157, 158, 159, 160, 161, 162, 164, 165, 169, 171, 172, 176, 179, 180, 185, 186, 187, 189, 190, 191, 192, 197, 198, 199, 201, 204, 205, 206, 208, 209, 211, 212, 214, 215, 216, 217, 218, 221, 222, 225, 226, 228, 229, 231, 233, 234, 235, 236, 240, 241, 242, 245, 246, 249, 251, 254, 257, 258, 261, 262, 265, 268, 270, 275, 276, 277, 289, 291, 294, 297, 298, 299, 300, 301, 302, 304, 306, 307, 308, 312, 314, 318, 319, 321, 323, 325, 327, 328, 329, 346, 348, 351, 354, 357, 359, 365, 366, 367, 387, 388, 393, 394, 397, 400, 403, 406, 407, 408, 409, 412, 413, 414, 415, 417, 418, 421, 423, 431, 432, 433, 457, 459, 462, 463, 464, 465, 467, 472, 473, 480, 486, 488, 489, 490], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15, 18, 20, 21, 49, 152, 167, 238, 282, 310, 331, 372, 438], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/rag/text_splitter.py": {"executed_lines": [1, 2, 3, 4, 5, 6, 8, 10, 11, 13, 30, 33, 34, 35, 36, 37, 39, 47, 58, 59, 63, 64, 66, 75, 81, 83, 92, 94, 95, 96, 97, 100, 101, 102, 103, 104, 105, 106, 107, 109, 112, 113, 116, 118, 120, 121, 123, 124, 125, 127, 128, 129, 131, 132, 134, 135, 136, 137, 150, 151, 152, 154, 155, 161, 163, 164, 168, 169, 170, 172, 173, 174, 179, 180, 181, 183, 184, 185, 187, 188, 189, 190, 192, 195, 197, 198, 202, 203, 204, 205, 207, 208, 209, 210, 212, 213, 219, 220, 222, 223, 242, 243, 245, 255, 262, 264, 265, 276, 286, 288, 291, 292, 293, 296, 297, 298, 299, 300, 302, 305, 306, 307, 309, 314, 315, 317, 318, 322, 325, 326], "summary": {"covered_lines": 124, "num_statements": 175, "percent_covered": 70.85714285714286, "percent_covered_display": "71", "missing_lines": 51, "excluded_lines": 4}, "missing_lines": [31, 41, 42, 43, 44, 45, 49, 50, 51, 52, 53, 54, 55, 56, 85, 86, 87, 88, 89, 90, 114, 139, 140, 141, 142, 143, 144, 145, 147, 148, 157, 158, 159, 165, 166, 175, 176, 177, 186, 196, 199, 200, 201, 215, 216, 217, 233, 310, 311, 312, 320], "excluded_lines": [58, 59, 60, 61], "functions": {"TextSplitter.__init__": {"executed_lines": [30, 33, 34, 35, 36, 37], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [31], "excluded_lines": []}, "TextSplitter.split_documents": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [41, 42, 43, 44, 45], "excluded_lines": []}, "TextSplitter.create_documents": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [49, 50, 51, 52, 53, 54, 55, 56], "excluded_lines": []}, "TextSplitter.split_text": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [60, 61]}, "RecursiveCharacterTextSplitter.__init__": {"executed_lines": [75, 81], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RecursiveCharacterTextSplitter.split_text": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [85, 86, 87, 88, 89, 90], "excluded_lines": []}, "RecursiveCharacterTextSplitter._split_text": {"executed_lines": [94, 95, 96, 97, 100, 101, 102, 103, 104, 105, 106, 107, 109, 112, 113, 116, 118, 120, 121, 123, 124, 125, 127, 128, 129, 131, 132, 134, 135, 136, 137, 150, 151, 152, 154, 155], "summary": {"covered_lines": 36, "num_statements": 49, "percent_covered": 73.46938775510205, "percent_covered_display": "73", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [114, 139, 140, 141, 142, 143, 144, 145, 147, 148, 157, 158, 159], "excluded_lines": []}, "RecursiveCharacterTextSplitter._merge_splits": {"executed_lines": [163, 164, 168, 169, 170, 172, 173, 174, 179, 180, 181, 183, 184, 185, 187, 188, 189, 190, 192, 195, 197, 198, 202, 203, 204, 205, 207, 208, 209, 210, 212, 213], "summary": {"covered_lines": 32, "num_statements": 45, "percent_covered": 71.11111111111111, "percent_covered_display": "71", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [165, 166, 175, 176, 177, 186, 196, 199, 200, 201, 215, 216, 217], "excluded_lines": []}, "EnhanceRecursiveCharacterTextSplitter.from_encoder": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [233], "excluded_lines": []}, "FixedRecursiveCharacterTextSplitter.__init__": {"executed_lines": [255, 262], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FixedRecursiveCharacterTextSplitter.from_encoder": {"executed_lines": [276], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FixedRecursiveCharacterTextSplitter.split_text": {"executed_lines": [288, 291, 292, 293, 296, 297, 298, 299, 300, 302, 305, 306, 307, 309, 314, 315, 317, 318, 322, 325, 326], "summary": {"covered_lines": 21, "num_statements": 25, "percent_covered": 84.0, "percent_covered_display": "84", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [310, 311, 312, 320], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 8, 10, 11, 13, 39, 47, 58, 59, 63, 64, 66, 83, 92, 161, 219, 220, 222, 223, 242, 243, 245, 264, 265, 286], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [58, 59]}}, "classes": {"TextSplitter": {"executed_lines": [30, 33, 34, 35, 36, 37], "summary": {"covered_lines": 6, "num_statements": 20, "percent_covered": 30.0, "percent_covered_display": "30", "missing_lines": 14, "excluded_lines": 2}, "missing_lines": [31, 41, 42, 43, 44, 45, 49, 50, 51, 52, 53, 54, 55, 56], "excluded_lines": [60, 61]}, "RecursiveCharacterTextSplitter": {"executed_lines": [75, 81, 94, 95, 96, 97, 100, 101, 102, 103, 104, 105, 106, 107, 109, 112, 113, 116, 118, 120, 121, 123, 124, 125, 127, 128, 129, 131, 132, 134, 135, 136, 137, 150, 151, 152, 154, 155, 163, 164, 168, 169, 170, 172, 173, 174, 179, 180, 181, 183, 184, 185, 187, 188, 189, 190, 192, 195, 197, 198, 202, 203, 204, 205, 207, 208, 209, 210, 212, 213], "summary": {"covered_lines": 70, "num_statements": 102, "percent_covered": 68.62745098039215, "percent_covered_display": "69", "missing_lines": 32, "excluded_lines": 0}, "missing_lines": [85, 86, 87, 88, 89, 90, 114, 139, 140, 141, 142, 143, 144, 145, 147, 148, 157, 158, 159, 165, 166, 175, 176, 177, 186, 196, 199, 200, 201, 215, 216, 217], "excluded_lines": []}, "EnhanceRecursiveCharacterTextSplitter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [233], "excluded_lines": []}, "FixedRecursiveCharacterTextSplitter": {"executed_lines": [255, 262, 276, 288, 291, 292, 293, 296, 297, 298, 299, 300, 302, 305, 306, 307, 309, 314, 315, 317, 318, 322, 325, 326], "summary": {"covered_lines": 24, "num_statements": 28, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [310, 311, 312, 320], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 8, 10, 11, 13, 39, 47, 58, 59, 63, 64, 66, 83, 92, 161, 219, 220, 222, 223, 242, 243, 245, 264, 265, 286], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [58, 59]}}}, "app/rag/vector_factory.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 80, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 80, "excluded_lines": 0}, "missing_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 14, 15, 16, 17, 18, 19, 21, 24, 31, 32, 33, 34, 36, 38, 39, 40, 42, 45, 47, 48, 49, 50, 52, 54, 63, 64, 66, 69, 70, 73, 76, 78, 80, 90, 91, 94, 102, 103, 106, 107, 108, 110, 118, 121, 122, 123, 124, 125, 126, 129, 130, 132, 134, 138, 140, 148, 149, 152, 153, 154, 157, 158, 159, 160, 161, 162, 163, 166, 169], "excluded_lines": [], "functions": {"Vector.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [31, 32, 33, 34], "excluded_lines": []}, "Vector._get_embeddings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [38, 39, 40], "excluded_lines": []}, "Vector._init_vector": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [45, 47, 48, 49, 50, 52], "excluded_lines": []}, "Vector._filter_duplicate_texts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [63, 64, 66, 69, 70, 73, 76, 78], "excluded_lines": []}, "Vector.process_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [90, 91, 94, 102, 103, 106, 107, 108], "excluded_lines": []}, "Vector.create": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [118, 121, 122, 123, 124, 125, 126, 129, 130, 132, 134, 138], "excluded_lines": []}, "Vector.add_texts": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [148, 149, 152, 153, 154, 157, 158, 159, 160, 161, 162, 163, 166, 169], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 14, 15, 16, 17, 18, 19, 21, 24, 36, 42, 54, 80, 110, 140], "excluded_lines": []}}, "classes": {"VectorType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Vector": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 55, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 55, "excluded_lines": 0}, "missing_lines": [31, 32, 33, 34, 38, 39, 40, 45, 47, 48, 49, 50, 52, 63, 64, 66, 69, 70, 73, 76, 78, 90, 91, 94, 102, 103, 106, 107, 108, 118, 121, 122, 123, 124, 125, 126, 129, 130, 132, 134, 138, 148, 149, 152, 153, 154, 157, 158, 159, 160, 161, 162, 163, 166, 169], "excluded_lines": []}, "": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 12, 14, 15, 16, 17, 18, 19, 21, 24, 36, 42, 54, 80, 110, 140], "excluded_lines": []}}}, "app/rag/vector_store.py": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 12, 13, 14, 15, 16, 18, 20, 21, 22, 25, 26, 29, 30, 33, 34, 37, 38, 41, 42, 105, 115, 131, 153, 191, 212, 313, 331, 361, 439, 471, 526, 560, 564, 588, 615, 654, 663], "summary": {"covered_lines": 35, "num_statements": 308, "percent_covered": 11.363636363636363, "percent_covered_display": "11", "missing_lines": 273, "excluded_lines": 15}, "missing_lines": [59, 60, 61, 62, 65, 66, 67, 70, 96, 99, 100, 102, 103, 107, 109, 110, 111, 112, 113, 117, 119, 120, 121, 124, 125, 126, 127, 128, 129, 134, 135, 136, 137, 139, 140, 142, 143, 144, 147, 149, 150, 151, 156, 157, 158, 160, 161, 164, 167, 169, 171, 172, 173, 176, 177, 178, 179, 182, 183, 184, 185, 187, 189, 193, 194, 196, 202, 203, 204, 205, 206, 209, 210, 214, 215, 216, 217, 218, 220, 222, 228, 263, 270, 274, 277, 287, 297, 306, 307, 309, 310, 311, 315, 316, 317, 318, 319, 320, 323, 324, 326, 327, 328, 329, 333, 334, 335, 338, 339, 341, 343, 351, 354, 355, 357, 358, 359, 369, 370, 371, 373, 375, 377, 383, 384, 385, 386, 388, 389, 392, 401, 402, 405, 406, 407, 408, 410, 411, 414, 415, 417, 419, 420, 423, 424, 425, 426, 427, 428, 429, 430, 432, 433, 435, 436, 437, 441, 442, 443, 446, 448, 449, 454, 455, 457, 458, 459, 461, 466, 467, 468, 469, 473, 474, 476, 477, 478, 480, 481, 483, 485, 486, 487, 488, 490, 491, 492, 494, 495, 497, 498, 499, 502, 505, 506, 508, 511, 512, 515, 518, 519, 521, 522, 523, 524, 528, 529, 530, 532, 533, 535, 537, 538, 539, 541, 543, 548, 549, 550, 551, 552, 553, 555, 556, 557, 558, 562, 566, 567, 568, 569, 571, 579, 580, 581, 583, 584, 585, 586, 590, 591, 593, 595, 596, 597, 598, 601, 608, 609, 611, 612, 613, 617, 619, 620, 623, 628, 637, 638, 639, 640, 646, 648, 650, 651, 652, 656, 657, 658, 659, 660, 661, 665], "excluded_lines": [21, 22, 23, 25, 26, 27, 29, 30, 31, 33, 34, 35, 37, 38, 39], "functions": {"BaseVectorStore.create_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [23]}, "BaseVectorStore.insert": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [27]}, "BaseVectorStore.search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [31]}, "BaseVectorStore.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [35]}, "BaseVectorStore.get_by_ids": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [39]}, "MilvusVectorStore.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [59, 60, 61, 62, 65, 66, 67, 70, 96, 99, 100, 102, 103], "excluded_lines": []}, "MilvusVectorStore._connect": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [107, 109, 110, 111, 112, 113], "excluded_lines": []}, "MilvusVectorStore._check_connection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [117, 119, 120, 121, 124, 125, 126, 127, 128, 129], "excluded_lines": []}, "MilvusVectorStore._get_index_config": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [134, 135, 136, 137, 139, 140, 142, 143, 144, 147, 149, 150, 151], "excluded_lines": []}, "MilvusVectorStore._ensure_index": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [156, 157, 158, 160, 161, 164, 167, 169, 171, 172, 173, 176, 177, 178, 179, 182, 183, 184, 185, 187, 189], "excluded_lines": []}, "MilvusVectorStore._get_collection_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [193, 194, 196, 202, 203, 204, 205, 206, 209, 210], "excluded_lines": []}, "MilvusVectorStore.create_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 19, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [214, 215, 216, 217, 218, 220, 222, 228, 263, 270, 274, 277, 287, 297, 306, 307, 309, 310, 311], "excluded_lines": []}, "MilvusVectorStore._ensure_collection_loaded": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [315, 316, 317, 318, 319, 320, 323, 324, 326, 327, 328, 329], "excluded_lines": []}, "MilvusVectorStore.insert": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [333, 334, 335, 338, 339, 341, 343, 351, 354, 355, 357, 358, 359], "excluded_lines": []}, "MilvusVectorStore.search_by_vector": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 39, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 39, "excluded_lines": 0}, "missing_lines": [369, 370, 371, 373, 375, 377, 383, 384, 385, 386, 388, 389, 392, 401, 402, 405, 406, 407, 408, 410, 411, 414, 415, 417, 419, 420, 423, 424, 425, 426, 427, 428, 429, 430, 432, 433, 435, 436, 437], "excluded_lines": []}, "MilvusVectorStore.get_by_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [441, 442, 443, 446, 448, 449, 454, 455, 457, 458, 459, 461, 466, 467, 468, 469], "excluded_lines": []}, "MilvusVectorStore.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 33, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 33, "excluded_lines": 0}, "missing_lines": [473, 474, 476, 477, 478, 480, 481, 483, 485, 486, 487, 488, 490, 491, 492, 494, 495, 497, 498, 499, 502, 505, 506, 508, 511, 512, 515, 518, 519, 521, 522, 523, 524], "excluded_lines": []}, "MilvusVectorStore.get_by_ids": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 21, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [528, 529, 530, 532, 533, 535, 537, 538, 539, 541, 543, 548, 549, 550, 551, 552, 553, 555, 556, 557, 558], "excluded_lines": []}, "MilvusVectorStore.search": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [617, 619, 620, 623, 628, 637, 638, 639, 640, 646, 648, 650, 651, 652], "excluded_lines": []}, "MilvusVectorStore.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [566, 567, 568, 569, 571, 579, 580, 581, 583, 584, 585, 586], "excluded_lines": []}, "MilvusVectorStore.insert_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [590, 591, 593, 595, 596, 597, 598, 601, 608, 609, 611, 612, 613], "excluded_lines": []}, "MilvusVectorStore.delete_by_segment_id": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [656, 657, 658, 659, 660, 661], "excluded_lines": []}, "MilvusVectorStore.get_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [665], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 12, 13, 14, 15, 16, 18, 20, 21, 22, 25, 26, 29, 30, 33, 34, 37, 38, 41, 42, 105, 115, 131, 153, 191, 212, 313, 331, 361, 439, 471, 526, 560, 564, 588, 615, 654, 663], "summary": {"covered_lines": 35, "num_statements": 35, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 10}, "missing_lines": [], "excluded_lines": [21, 22, 25, 26, 29, 30, 33, 34, 37, 38]}}, "classes": {"BaseVectorStore": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 5}, "missing_lines": [], "excluded_lines": [23, 27, 31, 35, 39]}, "MilvusVectorStore": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 273, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 273, "excluded_lines": 0}, "missing_lines": [59, 60, 61, 62, 65, 66, 67, 70, 96, 99, 100, 102, 103, 107, 109, 110, 111, 112, 113, 117, 119, 120, 121, 124, 125, 126, 127, 128, 129, 134, 135, 136, 137, 139, 140, 142, 143, 144, 147, 149, 150, 151, 156, 157, 158, 160, 161, 164, 167, 169, 171, 172, 173, 176, 177, 178, 179, 182, 183, 184, 185, 187, 189, 193, 194, 196, 202, 203, 204, 205, 206, 209, 210, 214, 215, 216, 217, 218, 220, 222, 228, 263, 270, 274, 277, 287, 297, 306, 307, 309, 310, 311, 315, 316, 317, 318, 319, 320, 323, 324, 326, 327, 328, 329, 333, 334, 335, 338, 339, 341, 343, 351, 354, 355, 357, 358, 359, 369, 370, 371, 373, 375, 377, 383, 384, 385, 386, 388, 389, 392, 401, 402, 405, 406, 407, 408, 410, 411, 414, 415, 417, 419, 420, 423, 424, 425, 426, 427, 428, 429, 430, 432, 433, 435, 436, 437, 441, 442, 443, 446, 448, 449, 454, 455, 457, 458, 459, 461, 466, 467, 468, 469, 473, 474, 476, 477, 478, 480, 481, 483, 485, 486, 487, 488, 490, 491, 492, 494, 495, 497, 498, 499, 502, 505, 506, 508, 511, 512, 515, 518, 519, 521, 522, 523, 524, 528, 529, 530, 532, 533, 535, 537, 538, 539, 541, 543, 548, 549, 550, 551, 552, 553, 555, 556, 557, 558, 562, 566, 567, 568, 569, 571, 579, 580, 581, 583, 584, 585, 586, 590, 591, 593, 595, 596, 597, 598, 601, 608, 609, 611, 612, 613, 617, 619, 620, 623, 628, 637, 638, 639, 640, 646, 648, 650, 651, 652, 656, 657, 658, 659, 660, 661, 665], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 12, 13, 14, 15, 16, 18, 20, 21, 22, 25, 26, 29, 30, 33, 34, 37, 38, 41, 42, 105, 115, 131, 153, 191, 212, 313, 331, 361, 439, 471, 526, 560, 564, 588, 615, 654, 663], "summary": {"covered_lines": 35, "num_statements": 35, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 10}, "missing_lines": [], "excluded_lines": [21, 22, 25, 26, 29, 30, 33, 34, 37, 38]}}}, "app/schemas/llm.py": {"executed_lines": [1, 2, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 60, 61, 62, 63, 64, 65, 66, 68, 73, 74, 75, 76, 77, 78, 80, 85, 86, 87, 88, 90, 94, 95, 96, 97, 98, 100, 104, 105, 106, 107, 108, 110], "summary": {"covered_lines": 71, "num_statements": 71, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 2, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 60, 61, 62, 63, 64, 65, 66, 68, 73, 74, 75, 76, 77, 78, 80, 85, 86, 87, 88, 90, 94, 95, 96, 97, 98, 100, 104, 105, 106, 107, 108, 110], "summary": {"covered_lines": 71, "num_statements": 71, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"LLMBase": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LLMCreate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LLMUpdate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LLMInDB": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LLMResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LLMTest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProviderInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 31, 32, 33, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 56, 60, 61, 62, 63, 64, 65, 66, 68, 73, 74, 75, 76, 77, 78, 80, 85, 86, 87, 88, 90, 94, 95, 96, 97, 98, 100, 104, 105, 106, 107, 108, 110], "summary": {"covered_lines": 71, "num_statements": 71, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/services/llm_service.py": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 15, 16, 17, 19, 36, 108, 119, 128, 152, 157, 172, 196, 260, 299, 359, 379, 393, 458, 635], "summary": {"covered_lines": 30, "num_statements": 312, "percent_covered": 9.615384615384615, "percent_covered_display": "10", "missing_lines": 282, "excluded_lines": 0}, "missing_lines": [22, 23, 28, 29, 30, 31, 33, 34, 67, 70, 71, 72, 73, 76, 77, 79, 82, 97, 98, 99, 100, 101, 104, 105, 106, 110, 111, 112, 113, 114, 115, 116, 117, 121, 122, 123, 124, 125, 126, 130, 131, 132, 134, 137, 138, 143, 144, 145, 150, 154, 155, 159, 160, 161, 162, 165, 166, 167, 168, 170, 174, 175, 176, 178, 180, 182, 183, 184, 185, 186, 187, 189, 190, 191, 192, 193, 194, 198, 199, 202, 203, 205, 206, 207, 209, 210, 213, 214, 219, 220, 221, 227, 233, 234, 235, 236, 239, 240, 242, 243, 250, 251, 253, 254, 255, 262, 267, 278, 279, 280, 281, 283, 284, 291, 292, 294, 301, 302, 305, 306, 312, 313, 322, 333, 334, 335, 336, 338, 339, 340, 342, 349, 350, 352, 353, 354, 361, 362, 363, 366, 372, 377, 382, 385, 388, 389, 391, 396, 432, 433, 434, 437, 438, 441, 442, 449, 450, 451, 452, 453, 454, 456, 460, 461, 462, 463, 466, 467, 468, 470, 471, 472, 473, 475, 477, 478, 480, 481, 482, 483, 484, 485, 487, 488, 489, 490, 491, 492, 495, 496, 497, 498, 499, 500, 501, 502, 505, 506, 507, 508, 511, 512, 513, 514, 517, 518, 519, 520, 522, 533, 534, 535, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 553, 555, 557, 558, 559, 560, 561, 563, 564, 565, 566, 567, 570, 571, 572, 573, 574, 575, 576, 577, 580, 581, 582, 583, 586, 587, 588, 589, 592, 593, 595, 596, 598, 609, 610, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 624, 625, 626, 628, 629, 630, 631, 632, 633], "excluded_lines": [], "functions": {"LLMService.__init__": {"executed_lines": [17], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LLMService.create_llm": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [22, 23, 28, 29, 30, 31, 33, 34], "excluded_lines": []}, "LLMService.register_discovered_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [67, 70, 71, 72, 73, 76, 77, 79, 82, 97, 98, 99, 100, 101, 104, 105, 106], "excluded_lines": []}, "LLMService.get_llm": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [110, 111, 112, 113, 114, 115, 116, 117], "excluded_lines": []}, "LLMService.get_llms": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [121, 122, 123, 124, 125, 126], "excluded_lines": []}, "LLMService.update_llm": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [130, 131, 132, 134, 137, 138, 143, 144, 145, 150], "excluded_lines": []}, "LLMService.delete_llm": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [154, 155], "excluded_lines": []}, "LLMService.get_default_llm": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [159, 160, 161, 162, 165, 166, 167, 168, 170], "excluded_lines": []}, "LLMService.test_llm": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [174, 175, 176, 178, 180, 182, 183, 184, 185, 186, 187, 189, 190, 191, 192, 193, 194], "excluded_lines": []}, "LLMService._test_embedding_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 28, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 28, "excluded_lines": 0}, "missing_lines": [198, 199, 202, 203, 205, 206, 207, 209, 210, 213, 214, 219, 220, 221, 227, 233, 234, 235, 236, 239, 240, 242, 243, 250, 251, 253, 254, 255], "excluded_lines": []}, "LLMService._test_openai_llm": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [262, 267, 278, 279, 280, 281, 283, 284, 291, 292, 294], "excluded_lines": []}, "LLMService._test_local_llm": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 20, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [301, 302, 305, 306, 312, 313, 322, 333, 334, 335, 336, 338, 339, 340, 342, 349, 350, 352, 353, 354], "excluded_lines": []}, "LLMService.set_default_llm": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [361, 362, 363, 366, 372, 377], "excluded_lines": []}, "LLMService.get_providers": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [382, 385, 388, 389, 391], "excluded_lines": []}, "LLMService.get_models_by_provider": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [396, 432, 433, 434, 437, 438, 441, 442, 449, 450, 451, 452, 453, 454, 456], "excluded_lines": []}, "LLMService.discover_local_models": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 120, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 120, "excluded_lines": 0}, "missing_lines": [460, 461, 462, 463, 466, 467, 468, 470, 471, 472, 473, 475, 477, 478, 480, 481, 482, 483, 484, 485, 487, 488, 489, 490, 491, 492, 495, 496, 497, 498, 499, 500, 501, 502, 505, 506, 507, 508, 511, 512, 513, 514, 517, 518, 519, 520, 522, 533, 534, 535, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 553, 555, 557, 558, 559, 560, 561, 563, 564, 565, 566, 567, 570, 571, 572, 573, 574, 575, 576, 577, 580, 581, 582, 583, 586, 587, 588, 589, 592, 593, 595, 596, 598, 609, 610, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 624, 625, 626, 628, 629, 630, 631, 632, 633], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 15, 16, 19, 36, 108, 119, 128, 152, 157, 172, 196, 260, 299, 359, 379, 393, 458, 635], "summary": {"covered_lines": 29, "num_statements": 29, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"LLMService": {"executed_lines": [17], "summary": {"covered_lines": 1, "num_statements": 283, "percent_covered": 0.35335689045936397, "percent_covered_display": "1", "missing_lines": 282, "excluded_lines": 0}, "missing_lines": [22, 23, 28, 29, 30, 31, 33, 34, 67, 70, 71, 72, 73, 76, 77, 79, 82, 97, 98, 99, 100, 101, 104, 105, 106, 110, 111, 112, 113, 114, 115, 116, 117, 121, 122, 123, 124, 125, 126, 130, 131, 132, 134, 137, 138, 143, 144, 145, 150, 154, 155, 159, 160, 161, 162, 165, 166, 167, 168, 170, 174, 175, 176, 178, 180, 182, 183, 184, 185, 186, 187, 189, 190, 191, 192, 193, 194, 198, 199, 202, 203, 205, 206, 207, 209, 210, 213, 214, 219, 220, 221, 227, 233, 234, 235, 236, 239, 240, 242, 243, 250, 251, 253, 254, 255, 262, 267, 278, 279, 280, 281, 283, 284, 291, 292, 294, 301, 302, 305, 306, 312, 313, 322, 333, 334, 335, 336, 338, 339, 340, 342, 349, 350, 352, 353, 354, 361, 362, 363, 366, 372, 377, 382, 385, 388, 389, 391, 396, 432, 433, 434, 437, 438, 441, 442, 449, 450, 451, 452, 453, 454, 456, 460, 461, 462, 463, 466, 467, 468, 470, 471, 472, 473, 475, 477, 478, 480, 481, 482, 483, 484, 485, 487, 488, 489, 490, 491, 492, 495, 496, 497, 498, 499, 500, 501, 502, 505, 506, 507, 508, 511, 512, 513, 514, 517, 518, 519, 520, 522, 533, 534, 535, 537, 538, 539, 540, 541, 542, 543, 544, 545, 546, 547, 548, 549, 550, 551, 553, 555, 557, 558, 559, 560, 561, 563, 564, 565, 566, 567, 570, 571, 572, 573, 574, 575, 576, 577, 580, 581, 582, 583, 586, 587, 588, 589, 592, 593, 595, 596, 598, 609, 610, 612, 613, 614, 615, 616, 617, 618, 619, 620, 621, 622, 624, 625, 626, 628, 629, 630, 631, 632, 633], "excluded_lines": []}, "": {"executed_lines": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 15, 16, 19, 36, 108, 119, 128, 152, 157, 172, 196, 260, 299, 359, 379, 393, 458, 635], "summary": {"covered_lines": 29, "num_statements": 29, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "app/services/user.py": {"executed_lines": [1, 13, 14, 15, 16, 17, 18, 19, 20, 23, 24, 26, 27, 34, 35, 64, 65, 94, 95, 138, 139, 178], "summary": {"covered_lines": 20, "num_statements": 57, "percent_covered": 35.08771929824562, "percent_covered_display": "35", "missing_lines": 37, "excluded_lines": 0}, "missing_lines": [47, 48, 49, 50, 51, 61, 62, 77, 78, 79, 80, 81, 91, 92, 110, 111, 112, 113, 122, 123, 124, 125, 134, 135, 136, 152, 154, 155, 157, 158, 159, 160, 162, 163, 164, 166, 167], "excluded_lines": [], "functions": {"UserService.get_by_email": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [47, 48, 49, 50, 51, 61, 62], "excluded_lines": []}, "UserService.get_by_username": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [77, 78, 79, 80, 81, 91, 92], "excluded_lines": []}, "UserService.create": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [110, 111, 112, 113, 122, 123, 124, 125, 134, 135, 136], "excluded_lines": []}, "UserService.authenticate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [152, 154, 155, 157, 158, 159, 160, 162, 163, 164, 166, 167], "excluded_lines": []}, "": {"executed_lines": [1, 13, 14, 15, 16, 17, 18, 19, 20, 23, 24, 26, 27, 34, 35, 64, 65, 94, 95, 138, 139, 178], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"UserService": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 37, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 37, "excluded_lines": 0}, "missing_lines": [47, 48, 49, 50, 51, 61, 62, 77, 78, 79, 80, 81, 91, 92, 110, 111, 112, 113, 122, 123, 124, 125, 134, 135, 136, 152, 154, 155, 157, 158, 159, 160, 162, 163, 164, 166, 167], "excluded_lines": []}, "": {"executed_lines": [1, 13, 14, 15, 16, 17, 18, 19, 20, 23, 24, 26, 27, 34, 35, 64, 65, 94, 95, 138, 139, 178], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}}, "totals": {"covered_lines": 905, "num_statements": 3465, "percent_covered": 26.118326118326117, "percent_covered_display": "26", "missing_lines": 2560, "excluded_lines": 19}}