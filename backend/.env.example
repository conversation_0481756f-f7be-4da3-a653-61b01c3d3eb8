# RAG Chat 配置文件示例
# 复制此文件为 .env 并根据您的环境修改配置

# ==================== 环境配置 ====================
# 环境类型：development, test, production
APP_ENV=development
ENVIRONMENT=development
DEBUG=true

# ==================== 基础配置 ====================
PROJECT_NAME=RAG Chat
API_V1_STR=/api/v1
VERSION=1.0.0
DESCRIPTION=基于 RAG 技术的智能聊天应用

# ==================== 安全配置 ====================
# 重要：生产环境必须使用强密钥（至少32字符）
SECRET_KEY=your-secret-key-here-at-least-32-characters
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# ==================== 数据库配置 ====================
# MongoDB配置
MONGODB_URL=mongodb://localhost:27017
MONGODB_DB=ragchat
MONGODB_DB_NAME=ragchat

# Milvus向量数据库配置
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_USER=
MILVUS_PASSWORD=
MILVUS_COLLECTION=documents
MILVUS_DIM=1536

# ==================== AI/LLM配置 ====================
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_EMBEDDINGS_MODEL=text-embedding-ada-002

# 本地模型配置
LOCAL_MODEL_URL=http://localhost:1234
EMBEDDING_API_BASE=http://localhost:1234/v1

# ==================== 文件存储配置 ====================
UPLOAD_DIR=data/uploads
MAX_UPLOAD_SIZE=10485760
CHROMA_PERSIST_DIR=data/db/chroma

# ==================== 文档处理配置 ====================
MAX_FILE_SIZE=104857600
PROCESSING_TIMEOUT=1800
MAX_SEGMENTS=100000
SPLITTER_TIMEOUT=300

# ==================== ETL配置 ====================
ETL_TYPE=Unstructured
UNSTRUCTURED_API_URL=your_unstructured_api_url
UNSTRUCTURED_API_KEY=your_unstructured_api_key

# ==================== CORS配置 ====================
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:5174

# ==================== 日志配置 ====================
LOGLEVEL=INFO
LOG_FILE=logs/app/app.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# ==================== 性能配置 ====================
MAX_WORKERS=4
BATCH_SIZE=50
CACHE_TTL=3600

# ==================== 监控配置 ====================
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30
