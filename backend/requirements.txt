# ============================================================================
# RAG-Chat 项目依赖配置
# ============================================================================

# ============================================================================
# 生产环境核心依赖
# ============================================================================

# Web框架
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.4.2
pydantic-settings==2.0.3
python-multipart==0.0.6
python-dotenv==1.0.0
starlette==0.27.0

# 数据库
motor==3.3.1
pymongo==4.6.1
pymilvus==2.3.3
redis>=4.0.0

# 认证
python-jose==3.3.0
passlib==1.7.4
bcrypt==4.0.1
email-validator==2.0.0

# RAG相关
openai==1.3.0
PyPDF2>=3.0.0
tiktoken==0.5.1
jieba>=0.42.1
langchain==0.2.17
langchain-community==0.2.19
unstructured==0.12.4
unstructured-inference==0.7.24
pillow-heif==0.15.0
pdf2image==1.17.0
pytesseract==0.3.10
python-magic==0.4.27
pdfminer.six==20231228
nltk==3.8.1
numpy>=1.24.0
pypdf==3.16.0
pypdfium2==4.25.0

# AI/ML工具
faiss-cpu==1.7.4
sentence-transformers>=2.2.2
torch>=2.0.0
transformers>=4.36.0

# 工具库
tenacity==8.2.3
anyio==3.7.1
httpx==0.25.1
aiofiles==23.2.1
async-timeout==4.0.3
loguru==0.7.0
psutil>=5.9.0

# ============================================================================
# 基础测试依赖（生产环境也可能需要）
# ============================================================================

# 基础测试框架
pytest>=7.0.0
pytest-asyncio==0.21.0
pytest-cov>=4.0.0