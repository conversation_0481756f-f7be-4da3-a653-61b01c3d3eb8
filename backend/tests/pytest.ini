[tool:pytest]
# pytest 配置文件

# 测试发现
testpaths = .
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 输出选项
addopts =
    -v
    --tb=short
    --strict-markers
    --strict-config
    --cov=app
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml:coverage.xml
    --cov-fail-under=70

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    api: API测试
    db: 数据库测试
    auth: 认证测试
    rag: RAG功能测试
    admin: 管理功能测试
    smoke: 冒烟测试
    regression: 回归测试
    comprehensive: 综合测试套件
    shell_replacement: 替代shell脚本的测试
    slow: 慢速测试

# 过滤警告
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:pymongo.*
    ignore::UserWarning:motor.*
    ignore::UserWarning

# 最小版本要求
minversion = 6.0

# 日志配置
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# 覆盖率配置
[coverage:run]
source = app
omit = 
    */tests/*
    */test_*
    */__pycache__/*
    */migrations/*
    */venv/*
    */env/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
