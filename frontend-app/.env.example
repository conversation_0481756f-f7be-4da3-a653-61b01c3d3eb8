# 前端应用配置文件示例
# 复制此文件为 .env 并根据您的环境修改配置

# ==================== 开发服务器配置 ====================
VITE_DEV_PORT=5173

# ==================== API配置 ====================
VITE_API_BASE_URL=http://localhost:8000
VITE_API_VERSION=/api/v1

# ==================== 认证配置 ====================
VITE_TOKEN_STORAGE_KEY=rag_chat_token
VITE_USER_STORAGE_KEY=rag_chat_user

# ==================== UI配置 ====================
VITE_APP_NAME=RAG Chat
VITE_DEFAULT_THEME=light
VITE_DEFAULT_LOCALE=zh-CN

# ==================== 上传配置 ====================
VITE_MAX_UPLOAD_SIZE=10485760  # 10MB
VITE_ALLOWED_FILE_TYPES=pdf,docx,txt,md

# ==================== 聊天配置 ====================
VITE_MAX_CHAT_HISTORY=100
VITE_MESSAGE_POLL_INTERVAL=1000  # 1秒

# ==================== 环境特定配置 ====================
# 开发环境
VITE_ENABLE_DEBUG=true
VITE_ENABLE_MOCK=false

# 生产环境（在生产环境中设置）
# VITE_ENABLE_DEBUG=false
# VITE_ENABLE_MOCK=false
# VITE_API_BASE_URL=https://your-production-api.com
