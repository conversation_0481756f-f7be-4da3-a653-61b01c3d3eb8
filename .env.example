# RAG Chat Application Environment Configuration
# 复制此文件为 .env 并填入实际值

# ============================================================================
# 应用基础配置
# ============================================================================
APP_ENV=development
APP_NAME="RAG Chat Application"
APP_VERSION=1.0.0
DEBUG=true

# API配置
API_V1_STR=/api/v1
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# ============================================================================
# 数据库配置
# ============================================================================

# MongoDB配置
MONGODB_URL=mongodb://localhost:27017/rag_chat_dev
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your_mongo_password

# Milvus配置
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_COLLECTION_NAME=rag_documents

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# ============================================================================
# AI模型配置
# ============================================================================

# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_API_BASE=https://api.openai.com/v1

# 嵌入模型配置
EMBEDDING_MODEL=text-embedding-ada-002
EMBEDDING_DIMENSION=1536

# LLM配置
LLM_MODEL=gpt-3.5-turbo
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=1000

# ============================================================================
# RAG配置
# ============================================================================

# 文档处理配置
DEFAULT_CHUNK_SIZE=1000
DEFAULT_CHUNK_OVERLAP=200
MAX_FILE_SIZE=52428800  # 50MB
SUPPORTED_DOCUMENT_FORMATS=.pdf,.txt,.md,.doc,.docx

# 检索配置
DEFAULT_TOP_K=5
SIMILARITY_THRESHOLD=0.7

# ============================================================================
# 安全配置
# ============================================================================

# JWT配置
JWT_SECRET_KEY=your_jwt_secret_key_here_make_it_very_long_and_random
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# 密码加密
PASSWORD_HASH_ALGORITHM=bcrypt

# ============================================================================
# 文件存储配置
# ============================================================================

# 上传配置
UPLOAD_DIR=./data/uploads
TEMP_DIR=./data/temp
CACHE_DIR=./data/cache

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=./logs

# ============================================================================
# 外部服务配置
# ============================================================================

# MinIO配置（用于Milvus）
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# ============================================================================
# 监控配置
# ============================================================================

# Grafana配置
GRAFANA_PASSWORD=your_grafana_password

# ============================================================================
# 生产环境特定配置
# ============================================================================

# 域名配置
DOMAIN=yourdomain.com
API_BASE_URL=https://api.yourdomain.com

# SSL配置
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# 备份配置
BACKUP_SCHEDULE="0 2 * * *"  # 每天凌晨2点
BACKUP_RETENTION_DAYS=30

# ============================================================================
# 开发环境特定配置
# ============================================================================

# 开发工具配置
RELOAD=true
WORKERS=1

# 测试配置
TEST_DATABASE_URL=mongodb://localhost:27017/rag_chat_test
TEST_MILVUS_COLLECTION=test_rag_documents
