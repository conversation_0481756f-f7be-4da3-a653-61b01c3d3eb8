name: CD Pipeline

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha,prefix={{branch}}-
    
    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64
    
    - name: Generate SBOM
      uses: anchore/sbom-action@v0
      with:
        image: ${{ steps.meta.outputs.tags }}
        format: spdx-json
        output-file: sbom.spdx.json
    
    - name: Upload SBOM
      uses: actions/upload-artifact@v3
      with:
        name: sbom
        path: sbom.spdx.json

  security-scan:
    runs-on: ubuntu-latest
    needs: build-and-push
    permissions:
      security-events: write
    
    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ needs.build-and-push.outputs.image-tag }}
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build-and-push, security-scan]
    if: github.ref == 'refs/heads/main' || github.event.inputs.environment == 'staging'
    environment: staging
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"
        # 这里添加实际的部署脚本
        # 例如：kubectl apply -f k8s/staging/ 或 docker-compose up -d
    
    - name: Run health check
      run: |
        echo "Running health check..."
        # 添加健康检查脚本
        # curl -f http://staging.example.com/health || exit 1
    
    - name: Run smoke tests
      run: |
        echo "Running smoke tests..."
        # 添加冒烟测试
        # python scripts/smoke_tests.py --env staging

  deploy-production:
    runs-on: ubuntu-latest
    needs: [build-and-push, security-scan, deploy-staging]
    if: startsWith(github.ref, 'refs/tags/v') || github.event.inputs.environment == 'production'
    environment: production
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
    
    - name: Deploy to production
      run: |
        echo "Deploying to production environment..."
        echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"
        # 这里添加实际的生产部署脚本
        # 例如：kubectl apply -f k8s/production/ 或 docker-compose up -d
    
    - name: Run health check
      run: |
        echo "Running production health check..."
        # 添加生产环境健康检查
        # curl -f http://api.example.com/health || exit 1
    
    - name: Run production smoke tests
      run: |
        echo "Running production smoke tests..."
        # 添加生产环境冒烟测试
        # python scripts/smoke_tests.py --env production
    
    - name: Notify deployment success
      run: |
        echo "Production deployment successful!"
        # 添加通知逻辑（Slack、邮件等）

  rollback:
    runs-on: ubuntu-latest
    if: failure() && (needs.deploy-staging.result == 'failure' || needs.deploy-production.result == 'failure')
    needs: [deploy-staging, deploy-production]
    environment: production
    
    steps:
    - name: Rollback deployment
      run: |
        echo "Rolling back deployment..."
        # 添加回滚逻辑
        # kubectl rollout undo deployment/rag-chat-app
    
    - name: Notify rollback
      run: |
        echo "Deployment rolled back due to failure"
        # 添加回滚通知逻辑

  cleanup:
    runs-on: ubuntu-latest
    if: always()
    needs: [build-and-push, deploy-staging, deploy-production]
    
    steps:
    - name: Clean up old images
      run: |
        echo "Cleaning up old container images..."
        # 添加清理逻辑，保留最近的N个版本
    
    - name: Generate deployment report
      run: |
        echo "Generating deployment report..."
        # 生成部署报告
        echo "Deployment completed at $(date)"
        echo "Image: ${{ needs.build-and-push.outputs.image-tag }}"
        echo "Digest: ${{ needs.build-and-push.outputs.image-digest }}"
