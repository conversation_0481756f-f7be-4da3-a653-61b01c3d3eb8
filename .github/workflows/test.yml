name: CI Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        python-version: [3.8, 3.9, "3.10", "3.11"]
    
    services:
      mongodb:
        image: mongo:5.0
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongo --eval 'db.adminCommand(\"ismaster\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      milvus:
        image: milvusdb/milvus:v2.3.0
        ports:
          - 19530:19530
        options: >-
          --health-cmd "curl -f http://localhost:9091/healthz"
          --health-interval 30s
          --health-timeout 10s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements*.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r backend/requirements.txt
        pip install -r backend/requirements-dev.txt
        pip install pytest pytest-cov pytest-asyncio httpx
    
    - name: Set up test environment
      run: |
        cp .env.test .env
        export APP_ENV=test
        mkdir -p logs/tests
        mkdir -p data/test
    
    - name: Wait for services
      run: |
        # Wait for MongoDB
        timeout 60 bash -c 'until nc -z localhost 27017; do sleep 1; done'
        # Wait for Milvus
        timeout 120 bash -c 'until nc -z localhost 19530; do sleep 1; done'
    
    - name: Run unit tests
      run: |
        cd backend
        python -m pytest tests/unit -v --tb=short -m unit
    
    - name: Run integration tests
      run: |
        cd backend
        python -m pytest tests/integration -v --tb=short -m integration
    
    - name: Run comprehensive tests
      run: |
        cd backend
        python -m pytest tests/integration/test_api_endpoints_comprehensive.py tests/integration/test_document_upload_comprehensive.py -v --tb=short
    
    - name: Run coverage tests
      run: |
        cd backend
        python -m pytest --cov=app --cov-report=xml --cov-report=html --cov-fail-under=70
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.python-version }}
        path: |
          backend/htmlcov/
          backend/coverage.xml
          backend/tests/logs/
    
    - name: Run performance tests
      run: |
        cd backend
        python -m pytest tests/performance -v --tb=short -m performance
      continue-on-error: true
    
    - name: Generate test report
      run: |
        python scripts/testing/run_tests.py --type all --output test_report.json
      continue-on-error: true
    
    - name: Upload test report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-report-${{ matrix.python-version }}
        path: test_report.json

  lint:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install black flake8 mypy isort ruff pre-commit
        pip install -r backend/requirements.txt
    
    - name: Run pre-commit hooks
      run: |
        cd backend
        pre-commit run --all-files
      continue-on-error: true

    - name: Run black
      run: |
        black --check backend/app/

    - name: Run ruff
      run: |
        ruff check backend/app/

    - name: Run flake8
      run: |
        flake8 backend/app/ --max-line-length=88 --extend-ignore=E203,W503

    - name: Run isort
      run: |
        isort --check-only backend/app/

    - name: Run mypy
      run: |
        mypy backend/app/ --ignore-missing-imports
      continue-on-error: true

  security:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: "3.10"
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
        pip install -r backend/requirements.txt
    
    - name: Run bandit security scan
      run: |
        bandit -r backend/app/ -f json -o bandit-report.json
      continue-on-error: true
    
    - name: Run safety check
      run: |
        safety check --json --output safety-report.json
      continue-on-error: true
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json
