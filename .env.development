# 开发环境配置文件
# 此文件包含开发环境的默认配置

# ==================== 环境配置 ====================
APP_ENV=development
ENVIRONMENT=development
DEBUG=true

# ==================== 基础配置 ====================
PROJECT_NAME=RAG Chat (Development)
API_V1_STR=/api/v1
VERSION=1.0.0-dev

# ==================== 安全配置 ====================
SECRET_KEY=dev-secret-key-change-in-production-32chars-minimum
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# ==================== 数据库配置 ====================
# MongoDB配置
MONGODB_URL=mongodb://localhost:27017
MONGODB_DB=ragchat_dev
MONGODB_DB_NAME=ragchat_dev

# Milvus向量数据库配置
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_USER=
MILVUS_PASSWORD=
MILVUS_COLLECTION=documents_dev
MILVUS_DIM=1536

# ==================== AI/LLM配置 ====================
# OpenAI配置（开发环境可以为空，使用本地模型）
OPENAI_API_KEY=
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_EMBEDDINGS_MODEL=text-embedding-ada-002

# 本地模型配置
LOCAL_MODEL_URL=http://localhost:1234
EMBEDDING_API_BASE=http://localhost:1234/v1

# ==================== 文件存储配置 ====================
UPLOAD_DIR=data/uploads
MAX_UPLOAD_SIZE=10485760
CHROMA_PERSIST_DIR=data/db/chroma

# ==================== 文档处理配置 ====================
MAX_FILE_SIZE=104857600
PROCESSING_TIMEOUT=1800
MAX_SEGMENTS=100000
SPLITTER_TIMEOUT=300

# ==================== ETL配置 ====================
ETL_TYPE=Unstructured
UNSTRUCTURED_API_URL=
UNSTRUCTURED_API_KEY=

# ==================== CORS配置 ====================
BACKEND_CORS_ORIGINS='["http://localhost:3000", "http://localhost:5173", "http://localhost:5174", "http://localhost:3001"]'

# ==================== 日志配置 ====================
LOGLEVEL=DEBUG
LOG_FILE=logs/app/app.log
LOG_ROTATION=1 day
LOG_RETENTION=7 days

# ==================== 性能配置 ====================
MAX_WORKERS=2
BATCH_SIZE=20
CACHE_TTL=1800

# ==================== 监控配置 ====================
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# ==================== RAG系统配置 ====================
RAG_TOP_K=3
RAG_SCORE_THRESHOLD=0.0
RAG_SCORE_THRESHOLD_ENABLED=false
RAG_MAX_RETRIES=3
RAG_RETRY_INTERVAL=5
RAG_REMOVE_HTML=true
RAG_REMOVE_EXTRA_SPACES=true
RAG_REMOVE_URLS=true
RAG_SEPARATOR=\n\n
RAG_MAX_TOKENS=512
RAG_CHUNK_OVERLAP=100
RAG_EMBEDDING_MODEL=text-embedding-nomic-embed-text-v1.5
RAG_EMBEDDING_API_BASE=http://localhost:1234
RAG_EMBEDDING_DIMENSION=768

# ==================== Redis缓存配置 ====================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_KEY_PREFIX=rag_cache:

# ==================== 管理员配置 ====================
ADMIN_TOKEN_EXPIRE_MINUTES=30
ADMIN_SECRET_KEY=
