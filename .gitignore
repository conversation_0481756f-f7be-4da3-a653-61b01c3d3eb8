# Python
__pycache__/
*.py[cod]
*$py.class
*.so
*.pyo
*.pyd
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
*.whl
pip-log.txt
pip-delete-this-directory.txt

# Virtual Environment
venv/
ENV/
.env

# Node.js
node_modules/
npm-debug.log
yarn-error.log
yarn-debug.log
.pnpm-debug.log

# Frontend build
frontend/dist/
frontend/build/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db
*.sublime-project
*.sublime-workspace

# Database
*.db
*.sqlite3

# Logs
logs/
*.log
!logs/**/.gitkeep

# Test
.coverage
htmlcov/
.pytest_cache/
coverage/

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local

# MongoDB
data/db/mongodb/*

# Milvus
data/db/milvus/*

# Uploaded files
data/uploads/*
uploads/
cache/

# Embeddings
data/embeddings/*

# Cache and vectors
data/cache/*
data/vectors/*
data/splitter_cache/
data/results/*
data/exports/*

# Data directories (consolidated)
/data/*
!/data/.gitkeep
!/data/README.md
!/data/*/
/data/*/**
!/data/**/.gitkeep

# Backend resources
backend/resources/data/*
!backend/resources/data/.gitkeep

# Document Processing
backend/tests/services/document_processing/logs/
backend/tests/services/document_processing/results/
backend/tests/services/document_processing/split_results/
backend/tests/services/document_processing/vectors/

# Temporary files
temp/
*.tmp
*.temp

# Debug files
backend/debug/
**/debug_*.py
**/test_*.py
**/test_*.html
verify_fix.py
final_verification.py

# API test files  
test_api_*.py
test_*_api*.py
test_frontend_*.py
test_complete_*.py
test_comprehensive_*.py
test_document_*.py
test_preview_*.py
test_path_*.py

# Frontend test pages
**/public/test-*.html
**/public/debug-*.html
**/TestCollections.tsx
**/SimpleTest.tsx

# Reports and analysis
*_REPORT.md
*_SUMMARY.md
*_DIAGNOSIS.md
*_ANALYSIS*.md
ROOT_DIRECTORY_REORGANIZATION_REPORT.md

/参考项目/