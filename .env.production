# 生产环境配置文件
# 此文件包含生产环境的配置模板
# ⚠️ 重要：在生产环境中，请修改所有敏感信息！

# ==================== 环境配置 ====================
APP_ENV=production
ENVIRONMENT=production
DEBUG=false

# ==================== 基础配置 ====================
PROJECT_NAME=RAG Chat
API_V1_STR=/api/v1
VERSION=1.0.0

# ==================== 安全配置 ====================
# ⚠️ 必须修改：生产环境必须使用强密钥
SECRET_KEY=CHANGE-THIS-TO-A-STRONG-SECRET-KEY-IN-PRODUCTION-AT-LEAST-32-CHARS
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# ==================== 数据库配置 ====================
# MongoDB配置（生产数据库）
MONGODB_URL=mongodb://localhost:27017
MONGODB_DB=ragchat
MONGODB_DB_NAME=ragchat

# Milvus向量数据库配置
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_USER=
MILVUS_PASSWORD=
MILVUS_COLLECTION=documents
MILVUS_DIM=1536

# ==================== AI/LLM配置 ====================
# OpenAI配置（生产环境需要真实API密钥）
OPENAI_API_KEY=your-real-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_EMBEDDINGS_MODEL=text-embedding-ada-002

# 本地模型配置
LOCAL_MODEL_URL=http://localhost:1234
EMBEDDING_API_BASE=http://localhost:1234/v1

# ==================== 文件存储配置 ====================
UPLOAD_DIR=data/uploads
MAX_UPLOAD_SIZE=104857600
CHROMA_PERSIST_DIR=data/db/chroma

# ==================== 文档处理配置 ====================
MAX_FILE_SIZE=104857600
PROCESSING_TIMEOUT=1800
MAX_SEGMENTS=100000
SPLITTER_TIMEOUT=300

# ==================== ETL配置 ====================
ETL_TYPE=Unstructured
UNSTRUCTURED_API_URL=your-unstructured-api-url
UNSTRUCTURED_API_KEY=your-unstructured-api-key

# ==================== CORS配置 ====================
# ⚠️ 修改为实际的前端域名
BACKEND_CORS_ORIGINS=https://your-frontend-domain.com,https://admin.your-domain.com

# ==================== 日志配置 ====================
LOGLEVEL=INFO
LOG_FILE=logs/app/app.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# ==================== 性能配置 ====================
MAX_WORKERS=4
BATCH_SIZE=50
CACHE_TTL=3600

# ==================== 监控配置 ====================
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# ==================== 生产环境专用配置 ====================
# SSL/TLS配置
SSL_ENABLED=true
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# 备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
