# Docker Compose配置 - 生产环境
version: '3.8'

services:
  # RAG应用后端
  rag-backend:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: rag-backend-prod
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=production
      - MONGODB_URL=mongodb://mongodb:27017/rag_chat_prod
      - MILVUS_HOST=milvus
      - MILVUS_PORT=19530
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_API_BASE=${OPENAI_API_BASE:-https://api.openai.com/v1}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - CORS_ORIGINS=${CORS_ORIGINS:-https://yourdomain.com}
    volumes:
      - app_data:/app/data
      - app_logs:/app/logs
    depends_on:
      - mongodb
      - milvus
      - redis
    networks:
      - rag-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # MongoDB数据库
  mongodb:
    image: mongo:5.0
    container_name: rag-mongodb-prod
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD}
      - MONGO_INITDB_DATABASE=rag_chat_prod
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
      - ./scripts/database/init-mongo-prod.js:/docker-entrypoint-initdb.d/init-mongo.js:ro
    networks:
      - rag-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Milvus向量数据库
  milvus:
    image: milvusdb/milvus:v2.3.0
    container_name: rag-milvus-prod
    environment:
      - ETCD_ENDPOINTS=etcd:2379
      - MINIO_ADDRESS=minio:9000
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
    volumes:
      - milvus_data:/var/lib/milvus
      - ./config/milvus.yaml:/milvus/configs/milvus.yaml:ro
    depends_on:
      - etcd
      - minio
    networks:
      - rag-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # Etcd (Milvus依赖)
  etcd:
    image: quay.io/coreos/etcd:v3.5.0
    container_name: rag-etcd-prod
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=4294967296
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    networks:
      - rag-network
    restart: unless-stopped

  # MinIO (Milvus依赖)
  minio:
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    container_name: rag-minio-prod
    environment:
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
    volumes:
      - minio_data:/data
    command: minio server /data --console-address ":9001"
    networks:
      - rag-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: rag-redis-prod
    command: redis-server --requirepass ${REDIS_PASSWORD}
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - rag-network
    restart: unless-stopped

  # 前端应用
  frontend-app:
    build:
      context: ./frontend-app
      dockerfile: Dockerfile.prod
    container_name: rag-frontend-app-prod
    environment:
      - VITE_API_BASE_URL=${API_BASE_URL}
    depends_on:
      - rag-backend
    networks:
      - rag-network
    restart: unless-stopped



  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: rag-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - rag-backend
      - frontend-app
    networks:
      - rag-network
    restart: unless-stopped

  # 监控 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: rag-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - rag-network
    restart: unless-stopped

  # 监控 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: rag-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - rag-network
    restart: unless-stopped

  # 日志收集 - Filebeat
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: rag-filebeat
    user: root
    volumes:
      - ./monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - app_logs:/var/log/app:ro
      - nginx_logs:/var/log/nginx:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - rag-network
    restart: unless-stopped

volumes:
  mongodb_data:
  mongodb_config:
  milvus_data:
  etcd_data:
  minio_data:
  redis_data:
  app_data:
  app_logs:
  nginx_logs:
  prometheus_data:
  grafana_data:

networks:
  rag-network:
    driver: bridge
