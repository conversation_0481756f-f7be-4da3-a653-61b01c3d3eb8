{"认证模块": {"base_path": "/api/v1/auth", "endpoints": [{"method": "POST", "path": "/register", "description": "用户注册", "auth_required": false, "admin_required": false}, {"method": "POST", "path": "/login", "description": "用户登录获取JWT令牌", "auth_required": false, "admin_required": false}]}, "大语言模型管理": {"base_path": "/api/v1/llm", "endpoints": [{"method": "GET", "path": "/", "description": "获取所有LLM模型", "auth_required": true, "admin_required": false}, {"method": "GET", "path": "/default", "description": "获取默认LLM模型", "auth_required": false, "admin_required": false}, {"method": "POST", "path": "/", "description": "创建LLM模型", "auth_required": true, "admin_required": true}, {"method": "PUT", "path": "/{llm_id}", "description": "更新LLM模型", "auth_required": true, "admin_required": true}, {"method": "DELETE", "path": "/{llm_id}", "description": "删除LLM模型", "auth_required": true, "admin_required": true}, {"method": "POST", "path": "/test", "description": "测试LLM模型", "auth_required": true, "admin_required": false}, {"method": "GET", "path": "/discover-models", "description": "发现本地模型", "auth_required": true, "admin_required": false}]}, "RAG检索增强生成": {"base_path": "/api/v1/rag", "endpoints": [{"method": "POST", "path": "/documents/upload", "description": "文档上传", "auth_required": true, "admin_required": false, "content_type": "multipart/form-data"}, {"method": "POST", "path": "/documents/search", "description": "文档搜索", "auth_required": true, "admin_required": false}, {"method": "POST", "path": "/chat", "description": "RAG聊天", "auth_required": true, "admin_required": false}, {"method": "GET", "path": "/documents", "description": "获取文档列表", "auth_required": true, "admin_required": false}, {"method": "DELETE", "path": "/documents/{document_id}", "description": "删除文档", "auth_required": true, "admin_required": false}, {"method": "GET", "path": "/status", "description": "检查RAG服务状态", "auth_required": true, "admin_required": false}]}, "文档集合管理": {"base_path": "/api/v1/rag/collections", "endpoints": [{"method": "GET", "path": "/", "description": "获取文档集列表", "auth_required": true, "admin_required": false}, {"method": "POST", "path": "/", "description": "创建文档集", "auth_required": true, "admin_required": false}, {"method": "GET", "path": "/{collection_id}", "description": "获取文档集详情", "auth_required": true, "admin_required": false}, {"method": "PUT", "path": "/{collection_id}", "description": "更新文档集", "auth_required": true, "admin_required": false}, {"method": "DELETE", "path": "/{collection_id}", "description": "删除文档集", "auth_required": true, "admin_required": false}]}, "管理模块": {"base_path": "/admin/api", "endpoints": [{"method": "POST", "path": "/auth/login", "description": "管理员登录", "auth_required": false, "admin_required": false}, {"method": "GET", "path": "/mongodb/collections", "description": "获取MongoDB集合", "auth_required": true, "admin_required": true}, {"method": "GET", "path": "/vector/status", "description": "获取向量存储状态", "auth_required": true, "admin_required": true}, {"method": "GET", "path": "/system/status", "description": "获取系统状态", "auth_required": true, "admin_required": true}]}}