# 项目文档索引

## 📚 文档导航

### 🏗️ 架构和设计
- [项目架构](ARCHITECTURE.md) - 系统架构设计文档
- [代码质量标准](CODE_QUALITY.md) - 代码质量和规范
- [依赖管理](DEPENDENCIES.md) - 项目依赖说明

### 🔧 API文档
- [API参考](API_REFERENCE.md) - 完整的API接口文档
- [API详细文档](api/API_DOCUMENTATION.md) - 详细的API使用说明
- [端点统一](api/ENDPOINT_UNIFICATION.md) - API端点规范

### 🚀 部署和运维
- [启动指南](deployment/STARTUP_GUIDE.md) - 项目启动和部署指南

### 🧪 测试文档
- [测试指南](testing/README.md) - 测试策略和方法
- [API测试报告](testing/api_test_report.md) - API测试结果

### 👨‍💻 开发文档
- [贡献指南](development/CONTRIBUTING.md) - 如何参与项目开发
- [开发工作流](development/WORKFLOW.md) - 开发流程和规范
- [开发日志](development/DEVLOG.md) - 开发过程记录
- [任务管理](development/TASKS.md) - 任务和待办事项

### 🔧 脚本和工具
- [脚本维护计划](SCRIPTS_MAINTENANCE_PLAN.md) - 脚本管理和维护
- [脚本维护摘要](SCRIPTS_MAINTENANCE_SUMMARY.md) - 脚本维护快速指南

### 🐛 修复记录
- [修复文档索引](fixes/README.md) - 所有修复记录的索引
- [UTF-8编码修复](fixes/utf8_encoding_fix.md) - 编码问题修复记录
- [API一致性修复](fixes/endpoint_consistency_fix_summary.md) - API一致性问题修复

### 📋 项目管理
- [项目重组报告](PROJECT_REORGANIZATION_REPORT.md) - 项目结构重组记录
- [实现摘要](implementation_summary.md) - 项目实现概述
- [统一文档API摘要](UNIFIED_DOCUMENT_API_SUMMARY.md) - 文档API统一说明

## 📝 文档维护

### 文档更新规范
1. 所有文档应包含创建/更新日期
2. 重要变更应在文档顶部添加变更日志
3. 过时的文档应及时更新或归档
4. 新功能必须同步更新相关文档

### 文档分类说明
- **架构文档**: 系统设计和架构相关
- **API文档**: 接口定义和使用说明
- **开发文档**: 开发流程和规范
- **部署文档**: 部署和运维指南
- **测试文档**: 测试策略和报告
- **修复文档**: 问题修复记录（历史参考）

## 🔄 最后更新
- 更新日期: 2025-06-29
- 更新内容: 创建文档索引和导航结构
