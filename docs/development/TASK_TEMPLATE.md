# 任务模板

使用此模板向 TASKS.md 添加新任务。

## 任务信息填写指南

```markdown
### 任务标题
- ⏱️ 任务描述
  - [ ] 子任务1
  - [ ] 子任务2
  - [ ] 子任务3
```

## 详细信息示例

以下是添加新任务时可以使用的详细示例:

### 基本任务

```markdown
- ⏱️ 移除 `discover.py` 和 `llm.py` 中的调试打印语句
```

### 包含子任务的任务

```markdown
- ⏱️ 实现文档处理和向量化功能
  - [ ] 实现文档解析
  - [ ] 实现文本分块
  - [ ] 实现向量化逻辑
  - [ ] 添加处理进度跟踪
```

### 具有前置条件的任务

```markdown
- 🛑 实现基于 RAG 的聊天功能 (依赖: 向量数据库集成)
  - [ ] 实现检索逻辑
  - [ ] 集成对话历史
  - [ ] 实现上下文组装
```

## 任务更新指南

当任务状态变化时，按以下方式更新:

### 开始任务

```diff
- ⏱️ 移除 `discover.py` 和 `llm.py` 中的调试打印语句
+ 🔄 移除 `discover.py` 和 `llm.py` 中的调试打印语句
```

### 完成任务

```diff
- 🔄 移除 `discover.py` 和 `llm.py` 中的调试打印语句
+ ✅ 移除 `discover.py` 和 `llm.py` 中的调试打印语句
```

### 任务阻塞

```diff
- 🔄 实现向量数据库集成
+ 🛑 实现向量数据库集成 (原因: 等待环境配置)
```

## 子任务完成

```diff
  - 🔄 实现文档处理和向量化功能
-   - [ ] 实现文档解析
+   - [x] 实现文档解析
    - [ ] 实现文本分块
    - [ ] 实现向量化逻辑
    - [ ] 添加处理进度跟踪
```

完成所有子任务后，记得同时更新主任务状态。 