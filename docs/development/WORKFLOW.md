# RAG-chat 工作流程指南

本文档描述了 RAG-chat 项目的工作流程，包括如何使用任务跟踪系统和记录开发过程。

## 文档体系

项目使用以下文档进行管理和记录：

1. **DEVLOG.md** - 开发日志，记录项目开发历程和重要里程碑
2. **TASKS.md** - 任务跟踪，列出所有待办、进行中和已完成的任务
3. **CONTRIBUTING.md** - 贡献指南，说明如何参与项目开发
4. **WORKFLOW.md** - 本文档，说明工作流程和记录方法

## 任务跟踪流程

### 1. 任务状态管理

TASKS.md 使用以下标记表示任务状态：

- 🔄 进行中：正在开发的任务
- ✅ 已完成：已完成并合并到主分支的任务
- ⏱️ 计划中：计划要做但尚未开始的任务
- 🛑 已阻塞：由于某些原因暂时无法继续的任务

### 2. 任务生命周期

1. **任务创建**：所有新任务首先添加到 TASKS.md，标记为"计划中"
2. **任务领取**：开发者领取任务时，将状态更新为"进行中"并提交更新
3. **任务完成**：任务完成后，更新状态为"已完成"并记录在 DEVLOG.md 中
4. **任务阻塞**：如遇阻碍，将状态更新为"已阻塞"并说明原因

### 3. 更新任务状态

修改任务状态时，需要：

1. 更新 TASKS.md 中的任务标记
2. 提交修改，提交信息格式为 `docs(tasks): 更新任务状态 - [简短描述]`
3. 如果任务完成，还需在 DEVLOG.md 中添加相应记录

## 开发日志维护

### 1. 日志条目格式

DEVLOG.md 中的每个新条目应包含：

- 日期（YYYY-MM-DD 格式）
- 标题（简要描述工作内容）
- 详细说明（列出具体完成的工作）
- 相关任务引用（如适用）

示例：

```markdown
### 2024-01-15 优化模型发现功能
- 修复了模型列表加载错误
- 改进了模型筛选逻辑
- 添加了模型版本显示

相关任务：#25, #31
```

### 2. 何时添加日志

应在以下情况添加新的日志条目：

- 完成一个或多个相关任务
- 发布新版本
- 解决重大问题
- 实现重要功能
- 进行架构调整或重构

### 3. 提交日志更新

更新开发日志时，提交信息格式为：
`docs(devlog): 添加 [日期] [简短描述] 日志条目`

## 版本发布流程

当准备发布新版本时：

1. 在 DEVLOG.md 中添加版本发布条目，包含版本号和主要更新
2. 确保 TASKS.md 中的相关任务已标记为"已完成"
3. 创建版本标签和发布说明

## 问题和拉取请求工作流

1. **问题报告**：使用 .github/ISSUE_TEMPLATE 中的模板创建问题
2. **功能请求**：使用功能请求模板提交新功能建议
3. **拉取请求**：创建拉取请求时引用相关问题，并说明解决方案

## 代码审查流程

代码审查应关注：

1. 代码质量和可读性
2. 测试覆盖率
3. 文档更新
4. 是否符合项目规范

## 示例工作流

以下是一个典型的工作流示例：

1. 从 TASKS.md 中选择一个"计划中"的任务
2. 更新任务状态为"进行中"并提交更改
3. 从 dev 分支创建新的功能分支
4. 完成开发工作
5. 更新任务状态为"已完成"
6. 在 DEVLOG.md 中添加相应记录
7. 创建拉取请求到 dev 分支
8. 通过代码审查后合并拉取请求

通过遵循这个工作流程，团队可以更好地协作并保持项目的可追踪性。 