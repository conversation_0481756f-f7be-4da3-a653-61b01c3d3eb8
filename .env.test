# 测试环境配置文件
# 此文件包含测试环境的配置，用于自动化测试

# ==================== 环境配置 ====================
APP_ENV=test
ENVIRONMENT=test
DEBUG=false

# ==================== 基础配置 ====================
PROJECT_NAME=RAG Chat (Test)
API_V1_STR=/api/v1
VERSION=1.0.0-test

# ==================== 安全配置 ====================
SECRET_KEY=test-secret-key-for-testing-only-32chars-minimum
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# ==================== 数据库配置 ====================
# MongoDB配置（测试数据库）
MONGODB_URL=mongodb://localhost:27017
MONGODB_DB=ragchat_test
MONGODB_DB_NAME=ragchat_test

# Milvus向量数据库配置（测试集合）
MILVUS_HOST=localhost
MILVUS_PORT=19530
MILVUS_USER=
MILVUS_PASSWORD=
MILVUS_COLLECTION=documents_test
MILVUS_DIM=1536

# ==================== AI/LLM配置 ====================
# OpenAI配置（测试环境使用mock或本地模型）
OPENAI_API_KEY=test-key-not-real
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_EMBEDDINGS_MODEL=text-embedding-ada-002

# 本地模型配置
LOCAL_MODEL_URL=http://localhost:1234
EMBEDDING_API_BASE=http://localhost:1234/v1

# ==================== 文件存储配置 ====================
UPLOAD_DIR=data/test/uploads
MAX_UPLOAD_SIZE=5242880
CHROMA_PERSIST_DIR=data/test/chroma

# ==================== 文档处理配置 ====================
MAX_FILE_SIZE=52428800
PROCESSING_TIMEOUT=300
MAX_SEGMENTS=10000
SPLITTER_TIMEOUT=60

# ==================== ETL配置 ====================
ETL_TYPE=Unstructured
UNSTRUCTURED_API_URL=
UNSTRUCTURED_API_KEY=

# ==================== CORS配置 ====================
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# ==================== 日志配置 ====================
LOGLEVEL=INFO
LOG_FILE=logs/tests/test.log
LOG_ROTATION=1 day
LOG_RETENTION=3 days

# ==================== 性能配置 ====================
MAX_WORKERS=1
BATCH_SIZE=10
CACHE_TTL=300

# ==================== 监控配置 ====================
ENABLE_METRICS=false
METRICS_PORT=9091
HEALTH_CHECK_INTERVAL=60

# ==================== 测试专用配置 ====================
TESTING=true
TEST_DATABASE_URL=mongodb://localhost:27017/ragchat_test
TEST_MILVUS_COLLECTION=test_documents
